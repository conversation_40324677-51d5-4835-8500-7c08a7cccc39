[package]
name = "cni-data-path"
version = "0.1.0"
edition = "2021"

[features]
ip_bits = []
default = ["ip_bits"]
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
aya-ebpf = "0.1.0"
aya-log-ebpf = "0.1.0"
cce-cni-common = { path = "../cce-cni-common", version="^0.1.0"}
memoffset = "0.9.1"
network-types = "0.0.5"

[[bin]]
name = "tc_master_slave_svc"
path = "ebpf/tc_master_slave_svc.rs"

[profile.dev]
opt-level = 3
debug = true
debug-assertions = false
overflow-checks = false
lto = true
panic = "abort"
incremental = false
codegen-units = 1
rpath = false

[profile.release]
lto = true
panic = "abort"
codegen-units = 1

