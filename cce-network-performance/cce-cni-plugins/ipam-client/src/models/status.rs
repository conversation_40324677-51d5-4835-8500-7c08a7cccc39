/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// Status : Status of an individual component  +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct Status {
    /// State the component is in
    #[serde(rename = "state")]
    state: Option<String>,
    /// Human readable status/error/warning message
    #[serde(rename = "msg")]
    msg: Option<String>,
}

impl Status {
    /// Status of an individual component  +k8s:deepcopy-gen=true
    pub fn new() -> Status {
        Status {
            state: None,
            msg: None,
        }
    }

    pub fn set_state(&mut self, state: String) {
        self.state = Some(state);
    }

    pub fn with_state(mut self, state: String) -> Status {
        self.state = Some(state);
        self
    }

    pub fn state(&self) -> Option<&String> {
        self.state.as_ref()
    }

    pub fn reset_state(&mut self) {
        self.state = None;
    }

    pub fn set_msg(&mut self, msg: String) {
        self.msg = Some(msg);
    }

    pub fn with_msg(mut self, msg: String) -> Status {
        self.msg = Some(msg);
        self
    }

    pub fn msg(&self) -> Option<&String> {
        self.msg.as_ref()
    }

    pub fn reset_msg(&mut self) {
        self.msg = None;
    }
}
