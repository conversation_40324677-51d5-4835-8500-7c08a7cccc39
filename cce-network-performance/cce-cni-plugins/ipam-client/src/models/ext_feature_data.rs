/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// ExtFeatureData : ExtFeatureData is a map

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ExtFeatureData {}

impl ExtFeatureData {
    /// ExtFeatureData is a map
    pub fn new() -> ExtFeatureData {
        ExtFeatureData {}
    }
}
