/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// StatusResponse : Health and status information of daemon  +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct StatusResponse {
    /// Status of CCE daemon
    #[serde(rename = "cce")]
    cce: Option<::models::Status>,
    /// Status of local container runtime
    #[serde(rename = "container-runtime")]
    container_runtime: Option<::models::Status>,
    /// Status of all endpoint controllers
    #[serde(rename = "controllers")]
    controllers: Option<::models::ControllerStatuses>,
    /// List of stale information in the status
    #[serde(rename = "stale")]
    stale: Option<::std::collections::HashMap<String, String>>,
    /// When supported by the API, this client ID should be used by the client when making another request to the server. See for example \"/cluster/nodes\".
    #[serde(rename = "client-id")]
    client_id: Option<String>,
}

impl StatusResponse {
    /// Health and status information of daemon  +k8s:deepcopy-gen=true
    pub fn new() -> StatusResponse {
        StatusResponse {
            cce: None,
            container_runtime: None,
            controllers: None,
            stale: None,
            client_id: None,
        }
    }

    pub fn set_cce(&mut self, cce: ::models::Status) {
        self.cce = Some(cce);
    }

    pub fn with_cce(mut self, cce: ::models::Status) -> StatusResponse {
        self.cce = Some(cce);
        self
    }

    pub fn cce(&self) -> Option<&::models::Status> {
        self.cce.as_ref()
    }

    pub fn reset_cce(&mut self) {
        self.cce = None;
    }

    pub fn set_container_runtime(&mut self, container_runtime: ::models::Status) {
        self.container_runtime = Some(container_runtime);
    }

    pub fn with_container_runtime(mut self, container_runtime: ::models::Status) -> StatusResponse {
        self.container_runtime = Some(container_runtime);
        self
    }

    pub fn container_runtime(&self) -> Option<&::models::Status> {
        self.container_runtime.as_ref()
    }

    pub fn reset_container_runtime(&mut self) {
        self.container_runtime = None;
    }

    pub fn set_controllers(&mut self, controllers: ::models::ControllerStatuses) {
        self.controllers = Some(controllers);
    }

    pub fn with_controllers(mut self, controllers: ::models::ControllerStatuses) -> StatusResponse {
        self.controllers = Some(controllers);
        self
    }

    pub fn controllers(&self) -> Option<&::models::ControllerStatuses> {
        self.controllers.as_ref()
    }

    pub fn reset_controllers(&mut self) {
        self.controllers = None;
    }

    pub fn set_stale(&mut self, stale: ::std::collections::HashMap<String, String>) {
        self.stale = Some(stale);
    }

    pub fn with_stale(
        mut self,
        stale: ::std::collections::HashMap<String, String>,
    ) -> StatusResponse {
        self.stale = Some(stale);
        self
    }

    pub fn stale(&self) -> Option<&::std::collections::HashMap<String, String>> {
        self.stale.as_ref()
    }

    pub fn reset_stale(&mut self) {
        self.stale = None;
    }

    pub fn set_client_id(&mut self, client_id: String) {
        self.client_id = Some(client_id);
    }

    pub fn with_client_id(mut self, client_id: String) -> StatusResponse {
        self.client_id = Some(client_id);
        self
    }

    pub fn client_id(&self) -> Option<&String> {
        self.client_id.as_ref()
    }

    pub fn reset_client_id(&mut self) {
        self.client_id = None;
    }
}
