/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// EndpointIdentifiers : Unique identifiers for this endpoint from outside CCE  +deepequal-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct EndpointIdentifiers {
    /// ID assigned by container runtime
    #[serde(rename = "container-id")]
    container_id: Option<String>,
    /// Name assigned to container
    #[serde(rename = "container-name")]
    container_name: Option<String>,
    /// K8s pod for this endpoint(Deprecated, use K8sPodName and K8sNamespace instead)
    #[serde(rename = "pod-name")]
    pod_name: Option<String>,
    /// K8s pod name for this endpoint
    #[serde(rename = "k8s-pod-name")]
    k8s_pod_name: Option<String>,
    /// K8s namespace for this endpoint
    #[serde(rename = "k8s-namespace")]
    k8s_namespace: Option<String>,
    /// K8s object id to indentifier a unique object
    #[serde(rename = "k8s-object-id")]
    k8s_object_id: Option<String>,
    /// External network ID, such as the ID of the ENI occupied by the container
    #[serde(rename = "external-identifier")]
    external_identifier: Option<String>,
    /// netns use by CNI
    #[serde(rename = "netns")]
    netns: Option<String>,
    /// device driver name
    #[serde(rename = "cnidriver")]
    cnidriver: Option<String>,
}

impl EndpointIdentifiers {
    /// Unique identifiers for this endpoint from outside CCE  +deepequal-gen=true
    pub fn new() -> EndpointIdentifiers {
        EndpointIdentifiers {
            container_id: None,
            container_name: None,
            pod_name: None,
            k8s_pod_name: None,
            k8s_namespace: None,
            k8s_object_id: None,
            external_identifier: None,
            netns: None,
            cnidriver: None,
        }
    }

    pub fn set_container_id(&mut self, container_id: String) {
        self.container_id = Some(container_id);
    }

    pub fn with_container_id(mut self, container_id: String) -> EndpointIdentifiers {
        self.container_id = Some(container_id);
        self
    }

    pub fn container_id(&self) -> Option<&String> {
        self.container_id.as_ref()
    }

    pub fn reset_container_id(&mut self) {
        self.container_id = None;
    }

    pub fn set_container_name(&mut self, container_name: String) {
        self.container_name = Some(container_name);
    }

    pub fn with_container_name(mut self, container_name: String) -> EndpointIdentifiers {
        self.container_name = Some(container_name);
        self
    }

    pub fn container_name(&self) -> Option<&String> {
        self.container_name.as_ref()
    }

    pub fn reset_container_name(&mut self) {
        self.container_name = None;
    }

    pub fn set_pod_name(&mut self, pod_name: String) {
        self.pod_name = Some(pod_name);
    }

    pub fn with_pod_name(mut self, pod_name: String) -> EndpointIdentifiers {
        self.pod_name = Some(pod_name);
        self
    }

    pub fn pod_name(&self) -> Option<&String> {
        self.pod_name.as_ref()
    }

    pub fn reset_pod_name(&mut self) {
        self.pod_name = None;
    }

    pub fn set_k8s_pod_name(&mut self, k8s_pod_name: String) {
        self.k8s_pod_name = Some(k8s_pod_name);
    }

    pub fn with_k8s_pod_name(mut self, k8s_pod_name: String) -> EndpointIdentifiers {
        self.k8s_pod_name = Some(k8s_pod_name);
        self
    }

    pub fn k8s_pod_name(&self) -> Option<&String> {
        self.k8s_pod_name.as_ref()
    }

    pub fn reset_k8s_pod_name(&mut self) {
        self.k8s_pod_name = None;
    }

    pub fn set_k8s_namespace(&mut self, k8s_namespace: String) {
        self.k8s_namespace = Some(k8s_namespace);
    }

    pub fn with_k8s_namespace(mut self, k8s_namespace: String) -> EndpointIdentifiers {
        self.k8s_namespace = Some(k8s_namespace);
        self
    }

    pub fn k8s_namespace(&self) -> Option<&String> {
        self.k8s_namespace.as_ref()
    }

    pub fn reset_k8s_namespace(&mut self) {
        self.k8s_namespace = None;
    }

    pub fn set_k8s_object_id(&mut self, k8s_object_id: String) {
        self.k8s_object_id = Some(k8s_object_id);
    }

    pub fn with_k8s_object_id(mut self, k8s_object_id: String) -> EndpointIdentifiers {
        self.k8s_object_id = Some(k8s_object_id);
        self
    }

    pub fn k8s_object_id(&self) -> Option<&String> {
        self.k8s_object_id.as_ref()
    }

    pub fn reset_k8s_object_id(&mut self) {
        self.k8s_object_id = None;
    }

    pub fn set_external_identifier(&mut self, external_identifier: String) {
        self.external_identifier = Some(external_identifier);
    }

    pub fn with_external_identifier(mut self, external_identifier: String) -> EndpointIdentifiers {
        self.external_identifier = Some(external_identifier);
        self
    }

    pub fn external_identifier(&self) -> Option<&String> {
        self.external_identifier.as_ref()
    }

    pub fn reset_external_identifier(&mut self) {
        self.external_identifier = None;
    }

    pub fn set_netns(&mut self, netns: String) {
        self.netns = Some(netns);
    }

    pub fn with_netns(mut self, netns: String) -> EndpointIdentifiers {
        self.netns = Some(netns);
        self
    }

    pub fn netns(&self) -> Option<&String> {
        self.netns.as_ref()
    }

    pub fn reset_netns(&mut self) {
        self.netns = None;
    }

    pub fn set_cnidriver(&mut self, cnidriver: String) {
        self.cnidriver = Some(cnidriver);
    }

    pub fn with_cnidriver(mut self, cnidriver: String) -> EndpointIdentifiers {
        self.cnidriver = Some(cnidriver);
        self
    }

    pub fn cnidriver(&self) -> Option<&String> {
        self.cnidriver.as_ref()
    }

    pub fn reset_cnidriver(&mut self) {
        self.cnidriver = None;
    }
}
