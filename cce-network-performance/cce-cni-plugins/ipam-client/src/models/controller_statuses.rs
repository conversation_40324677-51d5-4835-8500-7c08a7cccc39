/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// ControllerStatuses : Collection of controller statuses

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ControllerStatuses {}

impl ControllerStatuses {
    /// Collection of controller statuses
    pub fn new() -> ControllerStatuses {
        ControllerStatuses {}
    }
}
