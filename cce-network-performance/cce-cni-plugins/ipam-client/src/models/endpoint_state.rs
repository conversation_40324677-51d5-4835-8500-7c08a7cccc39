/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// EndpointState : State of endpoint

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct EndpointState {}

impl EndpointState {
    /// State of endpoint
    pub fn new() -> EndpointState {
        EndpointState {}
    }
}

// TODO enum
// List of EndpointState
//const (
//
//
//
//)
