/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// BandwidthOption : BandwidthOption is the bandwidth option of network +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct BandwidthOption {
    #[serde(rename = "mode")]
    mode: Option<String>,
    #[serde(rename = "ingress")]
    ingress: Option<i32>,
    #[serde(rename = "egress")]
    egress: Option<i32>,
}

impl BandwidthOption {
    /// BandwidthOption is the bandwidth option of network +k8s:deepcopy-gen=true
    pub fn new() -> BandwidthOption {
        BandwidthOption {
            mode: None,
            ingress: None,
            egress: None,
        }
    }

    pub fn set_mode(&mut self, mode: String) {
        self.mode = Some(mode);
    }

    pub fn with_mode(mut self, mode: String) -> BandwidthOption {
        self.mode = Some(mode);
        self
    }

    pub fn mode(&self) -> Option<&String> {
        self.mode.as_ref()
    }

    pub fn reset_mode(&mut self) {
        self.mode = None;
    }

    pub fn set_ingress(&mut self, ingress: i32) {
        self.ingress = Some(ingress);
    }

    pub fn with_ingress(mut self, ingress: i32) -> BandwidthOption {
        self.ingress = Some(ingress);
        self
    }

    pub fn ingress(&self) -> Option<&i32> {
        self.ingress.as_ref()
    }

    pub fn reset_ingress(&mut self) {
        self.ingress = None;
    }

    pub fn set_egress(&mut self, egress: i32) {
        self.egress = Some(egress);
    }

    pub fn with_egress(mut self, egress: i32) -> BandwidthOption {
        self.egress = Some(egress);
        self
    }

    pub fn egress(&self) -> Option<&i32> {
        self.egress.as_ref()
    }

    pub fn reset_egress(&mut self) {
        self.egress = None;
    }
}
