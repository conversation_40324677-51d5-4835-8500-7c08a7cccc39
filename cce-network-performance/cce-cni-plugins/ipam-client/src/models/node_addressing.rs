/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// NodeAddressing : Addressing information of a node for all address families  +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct NodeAddressing {
    #[serde(rename = "ipv6")]
    ipv6: Option<::models::NodeAddressingElement>,
    #[serde(rename = "ipv4")]
    ipv4: Option<::models::NodeAddressingElement>,
}

impl NodeAddressing {
    /// Addressing information of a node for all address families  +k8s:deepcopy-gen=true
    pub fn new() -> NodeAddressing {
        NodeAddressing {
            ipv6: None,
            ipv4: None,
        }
    }

    pub fn set_ipv6(&mut self, ipv6: ::models::NodeAddressingElement) {
        self.ipv6 = Some(ipv6);
    }

    pub fn with_ipv6(mut self, ipv6: ::models::NodeAddressingElement) -> NodeAddressing {
        self.ipv6 = Some(ipv6);
        self
    }

    pub fn ipv6(&self) -> Option<&::models::NodeAddressingElement> {
        self.ipv6.as_ref()
    }

    pub fn reset_ipv6(&mut self) {
        self.ipv6 = None;
    }

    pub fn set_ipv4(&mut self, ipv4: ::models::NodeAddressingElement) {
        self.ipv4 = Some(ipv4);
    }

    pub fn with_ipv4(mut self, ipv4: ::models::NodeAddressingElement) -> NodeAddressing {
        self.ipv4 = Some(ipv4);
        self
    }

    pub fn ipv4(&self) -> Option<&::models::NodeAddressingElement> {
        self.ipv4.as_ref()
    }

    pub fn reset_ipv4(&mut self) {
        self.ipv4 = None;
    }
}
