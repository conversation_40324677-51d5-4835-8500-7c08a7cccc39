/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// ControllerStatusConfiguration : Configuration of controller  +deepequal-gen=true +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ControllerStatusConfiguration {
    /// Regular synchronization interval
    #[serde(rename = "interval")]
    interval: Option<String>,
    /// Base error retry back-off time
    #[serde(rename = "error-retry-base")]
    error_retry_base: Option<String>,
    /// Retry on error
    #[serde(rename = "error-retry")]
    error_retry: Option<bool>,
}

impl ControllerStatusConfiguration {
    /// Configuration of controller  +deepequal-gen=true +k8s:deepcopy-gen=true
    pub fn new() -> ControllerStatusConfiguration {
        ControllerStatusConfiguration {
            interval: None,
            error_retry_base: None,
            error_retry: None,
        }
    }

    pub fn set_interval(&mut self, interval: String) {
        self.interval = Some(interval);
    }

    pub fn with_interval(mut self, interval: String) -> ControllerStatusConfiguration {
        self.interval = Some(interval);
        self
    }

    pub fn interval(&self) -> Option<&String> {
        self.interval.as_ref()
    }

    pub fn reset_interval(&mut self) {
        self.interval = None;
    }

    pub fn set_error_retry_base(&mut self, error_retry_base: String) {
        self.error_retry_base = Some(error_retry_base);
    }

    pub fn with_error_retry_base(
        mut self,
        error_retry_base: String,
    ) -> ControllerStatusConfiguration {
        self.error_retry_base = Some(error_retry_base);
        self
    }

    pub fn error_retry_base(&self) -> Option<&String> {
        self.error_retry_base.as_ref()
    }

    pub fn reset_error_retry_base(&mut self) {
        self.error_retry_base = None;
    }

    pub fn set_error_retry(&mut self, error_retry: bool) {
        self.error_retry = Some(error_retry);
    }

    pub fn with_error_retry(mut self, error_retry: bool) -> ControllerStatusConfiguration {
        self.error_retry = Some(error_retry);
        self
    }

    pub fn error_retry(&self) -> Option<&bool> {
        self.error_retry.as_ref()
    }

    pub fn reset_error_retry(&mut self) {
        self.error_retry = None;
    }
}
