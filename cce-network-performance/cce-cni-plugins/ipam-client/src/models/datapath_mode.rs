/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// DatapathMode : Datapath mode

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct DatapathMode {}

impl DatapathMode {
    /// Datapath mode
    pub fn new() -> DatapathMode {
        DatapathMode {}
    }
}

// TODO enum
// List of DatapathMode
//const (
//
//
//
//)
