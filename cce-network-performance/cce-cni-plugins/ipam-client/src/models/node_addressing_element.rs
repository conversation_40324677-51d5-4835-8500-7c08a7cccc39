/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// NodeAddressingElement : Addressing information

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct NodeAddressingElement {
    /// True if address family is enabled
    #[serde(rename = "enabled")]
    enabled: Option<bool>,
    /// IP address of node
    #[serde(rename = "ip")]
    ip: Option<String>,
    /// Address pool to be used for local endpoints
    #[serde(rename = "alloc-range")]
    alloc_range: Option<String>,
    /// Node address type, one of HostName, ExternalIP or InternalIP
    #[serde(rename = "address-type")]
    address_type: Option<String>,
}

impl NodeAddressingElement {
    /// Addressing information
    pub fn new() -> NodeAddressingElement {
        NodeAddressingElement {
            enabled: None,
            ip: None,
            alloc_range: None,
            address_type: None,
        }
    }

    pub fn set_enabled(&mut self, enabled: bool) {
        self.enabled = Some(enabled);
    }

    pub fn with_enabled(mut self, enabled: bool) -> NodeAddressingElement {
        self.enabled = Some(enabled);
        self
    }

    pub fn enabled(&self) -> Option<&bool> {
        self.enabled.as_ref()
    }

    pub fn reset_enabled(&mut self) {
        self.enabled = None;
    }

    pub fn set_ip(&mut self, ip: String) {
        self.ip = Some(ip);
    }

    pub fn with_ip(mut self, ip: String) -> NodeAddressingElement {
        self.ip = Some(ip);
        self
    }

    pub fn ip(&self) -> Option<&String> {
        self.ip.as_ref()
    }

    pub fn reset_ip(&mut self) {
        self.ip = None;
    }

    pub fn set_alloc_range(&mut self, alloc_range: String) {
        self.alloc_range = Some(alloc_range);
    }

    pub fn with_alloc_range(mut self, alloc_range: String) -> NodeAddressingElement {
        self.alloc_range = Some(alloc_range);
        self
    }

    pub fn alloc_range(&self) -> Option<&String> {
        self.alloc_range.as_ref()
    }

    pub fn reset_alloc_range(&mut self) {
        self.alloc_range = None;
    }

    pub fn set_address_type(&mut self, address_type: String) {
        self.address_type = Some(address_type);
    }

    pub fn with_address_type(mut self, address_type: String) -> NodeAddressingElement {
        self.address_type = Some(address_type);
        self
    }

    pub fn address_type(&self) -> Option<&String> {
        self.address_type.as_ref()
    }

    pub fn reset_address_type(&mut self) {
        self.address_type = None;
    }
}
