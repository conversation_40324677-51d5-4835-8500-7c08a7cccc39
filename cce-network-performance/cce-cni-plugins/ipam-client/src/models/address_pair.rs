/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// AddressPair : Addressing information of an endpoint

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct AddressPair {
    /// IPv4 address
    #[serde(rename = "ipv4")]
    ipv4: Option<String>,
    /// UUID of IPv4 expiration timer
    #[serde(rename = "ipv4-expiration-uuid")]
    ipv4_expiration_uuid: Option<String>,
    /// IPv6 address
    #[serde(rename = "ipv6")]
    ipv6: Option<String>,
    /// UUID of IPv6 expiration timer
    #[serde(rename = "ipv6-expiration-uuid")]
    ipv6_expiration_uuid: Option<String>,
}

impl AddressPair {
    /// Addressing information of an endpoint
    pub fn new() -> AddressPair {
        AddressPair {
            ipv4: None,
            ipv4_expiration_uuid: None,
            ipv6: None,
            ipv6_expiration_uuid: None,
        }
    }

    pub fn set_ipv4(&mut self, ipv4: String) {
        self.ipv4 = Some(ipv4);
    }

    pub fn with_ipv4(mut self, ipv4: String) -> AddressPair {
        self.ipv4 = Some(ipv4);
        self
    }

    pub fn ipv4(&self) -> Option<&String> {
        self.ipv4.as_ref()
    }

    pub fn reset_ipv4(&mut self) {
        self.ipv4 = None;
    }

    pub fn set_ipv4_expiration_uuid(&mut self, ipv4_expiration_uuid: String) {
        self.ipv4_expiration_uuid = Some(ipv4_expiration_uuid);
    }

    pub fn with_ipv4_expiration_uuid(mut self, ipv4_expiration_uuid: String) -> AddressPair {
        self.ipv4_expiration_uuid = Some(ipv4_expiration_uuid);
        self
    }

    pub fn ipv4_expiration_uuid(&self) -> Option<&String> {
        self.ipv4_expiration_uuid.as_ref()
    }

    pub fn reset_ipv4_expiration_uuid(&mut self) {
        self.ipv4_expiration_uuid = None;
    }

    pub fn set_ipv6(&mut self, ipv6: String) {
        self.ipv6 = Some(ipv6);
    }

    pub fn with_ipv6(mut self, ipv6: String) -> AddressPair {
        self.ipv6 = Some(ipv6);
        self
    }

    pub fn ipv6(&self) -> Option<&String> {
        self.ipv6.as_ref()
    }

    pub fn reset_ipv6(&mut self) {
        self.ipv6 = None;
    }

    pub fn set_ipv6_expiration_uuid(&mut self, ipv6_expiration_uuid: String) {
        self.ipv6_expiration_uuid = Some(ipv6_expiration_uuid);
    }

    pub fn with_ipv6_expiration_uuid(mut self, ipv6_expiration_uuid: String) -> AddressPair {
        self.ipv6_expiration_uuid = Some(ipv6_expiration_uuid);
        self
    }

    pub fn ipv6_expiration_uuid(&self) -> Option<&String> {
        self.ipv6_expiration_uuid.as_ref()
    }

    pub fn reset_ipv6_expiration_uuid(&mut self) {
        self.ipv6_expiration_uuid = None;
    }
}
