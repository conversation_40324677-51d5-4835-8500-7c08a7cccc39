/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// ControllerStatusStatus : Current status of controller  +k8s:deepcopy-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct ControllerStatusStatus {
    /// Total number of successful runs
    #[serde(rename = "success-count")]
    success_count: Option<i32>,
    /// Timestamp of last success
    #[serde(rename = "last-success-timestamp")]
    last_success_timestamp: Option<String>,
    /// Total number of failed runs
    #[serde(rename = "failure-count")]
    failure_count: Option<i32>,
    /// Timestamp of last error
    #[serde(rename = "last-failure-timestamp")]
    last_failure_timestamp: Option<String>,
    /// Number of consecutive errors since last success
    #[serde(rename = "consecutive-failure-count")]
    consecutive_failure_count: Option<i32>,
    /// Error message of last failed run
    #[serde(rename = "last-failure-msg")]
    last_failure_msg: Option<String>,
}

impl ControllerStatusStatus {
    /// Current status of controller  +k8s:deepcopy-gen=true
    pub fn new() -> ControllerStatusStatus {
        ControllerStatusStatus {
            success_count: None,
            last_success_timestamp: None,
            failure_count: None,
            last_failure_timestamp: None,
            consecutive_failure_count: None,
            last_failure_msg: None,
        }
    }

    pub fn set_success_count(&mut self, success_count: i32) {
        self.success_count = Some(success_count);
    }

    pub fn with_success_count(mut self, success_count: i32) -> ControllerStatusStatus {
        self.success_count = Some(success_count);
        self
    }

    pub fn success_count(&self) -> Option<&i32> {
        self.success_count.as_ref()
    }

    pub fn reset_success_count(&mut self) {
        self.success_count = None;
    }

    pub fn set_last_success_timestamp(&mut self, last_success_timestamp: String) {
        self.last_success_timestamp = Some(last_success_timestamp);
    }

    pub fn with_last_success_timestamp(
        mut self,
        last_success_timestamp: String,
    ) -> ControllerStatusStatus {
        self.last_success_timestamp = Some(last_success_timestamp);
        self
    }

    pub fn last_success_timestamp(&self) -> Option<&String> {
        self.last_success_timestamp.as_ref()
    }

    pub fn reset_last_success_timestamp(&mut self) {
        self.last_success_timestamp = None;
    }

    pub fn set_failure_count(&mut self, failure_count: i32) {
        self.failure_count = Some(failure_count);
    }

    pub fn with_failure_count(mut self, failure_count: i32) -> ControllerStatusStatus {
        self.failure_count = Some(failure_count);
        self
    }

    pub fn failure_count(&self) -> Option<&i32> {
        self.failure_count.as_ref()
    }

    pub fn reset_failure_count(&mut self) {
        self.failure_count = None;
    }

    pub fn set_last_failure_timestamp(&mut self, last_failure_timestamp: String) {
        self.last_failure_timestamp = Some(last_failure_timestamp);
    }

    pub fn with_last_failure_timestamp(
        mut self,
        last_failure_timestamp: String,
    ) -> ControllerStatusStatus {
        self.last_failure_timestamp = Some(last_failure_timestamp);
        self
    }

    pub fn last_failure_timestamp(&self) -> Option<&String> {
        self.last_failure_timestamp.as_ref()
    }

    pub fn reset_last_failure_timestamp(&mut self) {
        self.last_failure_timestamp = None;
    }

    pub fn set_consecutive_failure_count(&mut self, consecutive_failure_count: i32) {
        self.consecutive_failure_count = Some(consecutive_failure_count);
    }

    pub fn with_consecutive_failure_count(
        mut self,
        consecutive_failure_count: i32,
    ) -> ControllerStatusStatus {
        self.consecutive_failure_count = Some(consecutive_failure_count);
        self
    }

    pub fn consecutive_failure_count(&self) -> Option<&i32> {
        self.consecutive_failure_count.as_ref()
    }

    pub fn reset_consecutive_failure_count(&mut self) {
        self.consecutive_failure_count = None;
    }

    pub fn set_last_failure_msg(&mut self, last_failure_msg: String) {
        self.last_failure_msg = Some(last_failure_msg);
    }

    pub fn with_last_failure_msg(mut self, last_failure_msg: String) -> ControllerStatusStatus {
        self.last_failure_msg = Some(last_failure_msg);
        self
    }

    pub fn last_failure_msg(&self) -> Option<&String> {
        self.last_failure_msg.as_ref()
    }

    pub fn reset_last_failure_msg(&mut self) {
        self.last_failure_msg = None;
    }
}
