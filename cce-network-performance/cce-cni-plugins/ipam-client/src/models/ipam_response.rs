/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// IpamResponse : IPAM configuration of an endpoint

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct IpamResponse {
    #[serde(rename = "address")]
    address: ::models::AddressPair,
    #[serde(rename = "ipv4")]
    ipv4: Option<::models::IpamAddressResponse>,
    #[serde(rename = "ipv6")]
    ipv6: Option<::models::IpamAddressResponse>,
    #[serde(rename = "host-addressing")]
    host_addressing: ::models::NodeAddressing,
}

impl IpamResponse {
    /// IPAM configuration of an endpoint
    pub fn new(
        address: ::models::AddressPair,
        host_addressing: ::models::NodeAddressing,
    ) -> IpamResponse {
        IpamResponse {
            address: address,
            ipv4: None,
            ipv6: None,
            host_addressing: host_addressing,
        }
    }

    pub fn set_address(&mut self, address: ::models::AddressPair) {
        self.address = address;
    }

    pub fn with_address(mut self, address: ::models::AddressPair) -> IpamResponse {
        self.address = address;
        self
    }

    pub fn address(&self) -> &::models::AddressPair {
        &self.address
    }

    pub fn set_ipv4(&mut self, ipv4: ::models::IpamAddressResponse) {
        self.ipv4 = Some(ipv4);
    }

    pub fn with_ipv4(mut self, ipv4: ::models::IpamAddressResponse) -> IpamResponse {
        self.ipv4 = Some(ipv4);
        self
    }

    pub fn ipv4(&self) -> Option<&::models::IpamAddressResponse> {
        self.ipv4.as_ref()
    }

    pub fn reset_ipv4(&mut self) {
        self.ipv4 = None;
    }

    pub fn set_ipv6(&mut self, ipv6: ::models::IpamAddressResponse) {
        self.ipv6 = Some(ipv6);
    }

    pub fn with_ipv6(mut self, ipv6: ::models::IpamAddressResponse) -> IpamResponse {
        self.ipv6 = Some(ipv6);
        self
    }

    pub fn ipv6(&self) -> Option<&::models::IpamAddressResponse> {
        self.ipv6.as_ref()
    }

    pub fn reset_ipv6(&mut self) {
        self.ipv6 = None;
    }

    pub fn set_host_addressing(&mut self, host_addressing: ::models::NodeAddressing) {
        self.host_addressing = host_addressing;
    }

    pub fn with_host_addressing(
        mut self,
        host_addressing: ::models::NodeAddressing,
    ) -> IpamResponse {
        self.host_addressing = host_addressing;
        self
    }

    pub fn host_addressing(&self) -> &::models::NodeAddressing {
        &self.host_addressing
    }
}
