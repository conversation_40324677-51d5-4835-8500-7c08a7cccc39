/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

/// EndpointStatusChange : Indication of a change of status  +deepequal-gen=true

#[allow(unused_imports)]
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct EndpointStatusChange {
    /// Timestamp when status change occurred
    #[serde(rename = "timestamp")]
    timestamp: Option<String>,
    /// Code indicate type of status change
    #[serde(rename = "code")]
    code: Option<String>,
    /// Status message
    #[serde(rename = "message")]
    message: Option<String>,
    #[serde(rename = "state")]
    state: Option<::models::EndpointState>,
}

impl EndpointStatusChange {
    /// Indication of a change of status  +deepequal-gen=true
    pub fn new() -> EndpointStatusChange {
        EndpointStatusChange {
            timestamp: None,
            code: None,
            message: None,
            state: None,
        }
    }

    pub fn set_timestamp(&mut self, timestamp: String) {
        self.timestamp = Some(timestamp);
    }

    pub fn with_timestamp(mut self, timestamp: String) -> EndpointStatusChange {
        self.timestamp = Some(timestamp);
        self
    }

    pub fn timestamp(&self) -> Option<&String> {
        self.timestamp.as_ref()
    }

    pub fn reset_timestamp(&mut self) {
        self.timestamp = None;
    }

    pub fn set_code(&mut self, code: String) {
        self.code = Some(code);
    }

    pub fn with_code(mut self, code: String) -> EndpointStatusChange {
        self.code = Some(code);
        self
    }

    pub fn code(&self) -> Option<&String> {
        self.code.as_ref()
    }

    pub fn reset_code(&mut self) {
        self.code = None;
    }

    pub fn set_message(&mut self, message: String) {
        self.message = Some(message);
    }

    pub fn with_message(mut self, message: String) -> EndpointStatusChange {
        self.message = Some(message);
        self
    }

    pub fn message(&self) -> Option<&String> {
        self.message.as_ref()
    }

    pub fn reset_message(&mut self) {
        self.message = None;
    }

    pub fn set_state(&mut self, state: ::models::EndpointState) {
        self.state = Some(state);
    }

    pub fn with_state(mut self, state: ::models::EndpointState) -> EndpointStatusChange {
        self.state = Some(state);
        self
    }

    pub fn state(&self) -> Option<&::models::EndpointState> {
        self.state.as_ref()
    }

    pub fn reset_state(&mut self) {
        self.state = None;
    }
}
