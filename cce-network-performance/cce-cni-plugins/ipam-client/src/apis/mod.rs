use hyper;
use serde;
use serde_json;

#[derive(Debug)]
pub enum Error<T> {
    Hyper(hyper::Error),
    <PERSON><PERSON>(serde_json::Error),
    ApiError(ApiError<T>),
}

#[derive(Debug)]
pub struct ApiError<T> {
    pub code: hyper::StatusCode,
    pub content: Option<T>,
}

impl<'de, T> From<(hyper::StatusCode, &'de [u8])> for Error<T>
where
    T: serde::Deserialize<'de>,
{
    fn from(e: (hyper::StatusCode, &'de [u8])) -> Self {
        if e.1.len() == 0 {
            return Error::ApiError(ApiError {
                code: e.0,
                content: None,
            });
        }
        match serde_json::from_slice::<T>(e.1) {
            Ok(t) => Error::ApiError(ApiError {
                code: e.0,
                content: Some(t),
            }),
            Err(e) => Error::from(e),
        }
    }
}

impl<T> From<hyper::Error> for Error<T> {
    fn from(e: hyper::Error) -> Self {
        return Error::Hyper(e);
    }
}

impl<T> From<serde_json::Error> for Error<T> {
    fn from(e: serde_json::Error) -> Self {
        return Error::Serde(e);
    }
}

use super::models::*;

mod daemon_api;
pub use self::daemon_api::{DaemonApi, DaemonApiClient};
mod endpoint_api;
pub use self::endpoint_api::{EndpointApi, EndpointApiClient};
mod eni_api;
pub use self::eni_api::{EniApi, EniApiClient};
mod ipam_api;
pub use self::ipam_api::{IpamApi, IpamApiClient};
mod metrics_api;
pub use self::metrics_api::{MetricsApi, MetricsApiClient};

pub mod client;
pub mod configuration;
