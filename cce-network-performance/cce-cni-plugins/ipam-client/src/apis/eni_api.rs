/*
 * Cce API
 *
 * CCE
 *
 * OpenAPI spec version: v1beta
 *
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

use std::borrow::Borrow;
use std::borrow::Cow;
use std::collections::HashMap;
use std::rc::Rc;

use futures;
use futures::{Future, Stream};
use hyper;
use serde_json;

use hyper::header::UserAgent;

use super::{configuration, Error};

pub struct EniApiClient<C: hyper::client::Connect> {
    configuration: Rc<configuration::Configuration<C>>,
}

impl<C: hyper::client::Connect> EniApiClient<C> {
    pub fn new(configuration: Rc<configuration::Configuration<C>>) -> EniApiClient<C> {
        EniApiClient {
            configuration: configuration,
        }
    }
}

pub trait EniApi {
    fn eni_delete(
        &self,
        owner: &str,
        container_id: &str,
        netns: &str,
        ifname: &str,
    ) -> Box<Future<Item = (), Error = Error<serde_json::Value>>>;
    fn eni_post(
        &self,
        eni: ::models::Eni,
        owner: &str,
        container_id: &str,
        netns: &str,
        ifname: &str,
    ) -> Box<Future<Item = ::models::IpamResponse, Error = Error<serde_json::Value>>>;
}

impl<C: hyper::client::Connect> EniApi for EniApiClient<C> {
    fn eni_delete(
        &self,
        owner: &str,
        container_id: &str,
        netns: &str,
        ifname: &str,
    ) -> Box<Future<Item = (), Error = Error<serde_json::Value>>> {
        let configuration: &configuration::Configuration<C> = self.configuration.borrow();

        let method = hyper::Method::Delete;

        let query_string = {
            let mut query = ::url::form_urlencoded::Serializer::new(String::new());
            query.append_pair("owner", &owner.to_string());
            query.append_pair("containerID", &container_id.to_string());
            query.append_pair("netns", &netns.to_string());
            query.append_pair("ifname", &ifname.to_string());
            query.finish()
        };
        let uri_str = format!("{}/eni?{}", configuration.base_path, query_string);

        // TODO(farcaller): handle error
        // if let Err(e) = uri {
        //     return Box::new(futures::future::err(e));
        // }
        let mut uri: hyper::Uri = uri_str.parse().unwrap();

        let mut req = hyper::Request::new(method, uri);

        if let Some(ref user_agent) = configuration.user_agent {
            req.headers_mut()
                .set(UserAgent::new(Cow::Owned(user_agent.clone())));
        }

        // send request
        Box::new(
            configuration
                .client
                .request(req)
                .map_err(|e| Error::from(e))
                .and_then(|resp| {
                    let status = resp.status();
                    resp.body()
                        .concat2()
                        .and_then(move |body| Ok((status, body)))
                        .map_err(|e| Error::from(e))
                })
                .and_then(|(status, body)| {
                    if status.is_success() {
                        Ok(body)
                    } else {
                        Err(Error::from((status, &*body)))
                    }
                })
                .and_then(|_| futures::future::ok(())),
        )
    }

    fn eni_post(
        &self,
        eni: ::models::Eni,
        owner: &str,
        container_id: &str,
        netns: &str,
        ifname: &str,
    ) -> Box<Future<Item = ::models::IpamResponse, Error = Error<serde_json::Value>>> {
        let configuration: &configuration::Configuration<C> = self.configuration.borrow();

        let method = hyper::Method::Post;

        let query_string = {
            let mut query = ::url::form_urlencoded::Serializer::new(String::new());
            query.append_pair("owner", &owner.to_string());
            query.append_pair("containerID", &container_id.to_string());
            query.append_pair("netns", &netns.to_string());
            query.append_pair("ifname", &ifname.to_string());
            query.finish()
        };
        let uri_str = format!("{}/eni?{}", configuration.base_path, query_string);

        // TODO(farcaller): handle error
        // if let Err(e) = uri {
        //     return Box::new(futures::future::err(e));
        // }
        let mut uri: hyper::Uri = uri_str.parse().unwrap();

        let mut req = hyper::Request::new(method, uri);

        if let Some(ref user_agent) = configuration.user_agent {
            req.headers_mut()
                .set(UserAgent::new(Cow::Owned(user_agent.clone())));
        }

        let serialized = serde_json::to_string(&eni).unwrap();
        req.headers_mut().set(hyper::header::ContentType::json());
        req.headers_mut()
            .set(hyper::header::ContentLength(serialized.len() as u64));
        req.set_body(serialized);

        // send request
        Box::new(
            configuration
                .client
                .request(req)
                .map_err(|e| Error::from(e))
                .and_then(|resp| {
                    let status = resp.status();
                    resp.body()
                        .concat2()
                        .and_then(move |body| Ok((status, body)))
                        .map_err(|e| Error::from(e))
                })
                .and_then(|(status, body)| {
                    if status.is_success() {
                        Ok(body)
                    } else {
                        Err(Error::from((status, &*body)))
                    }
                })
                .and_then(|body| {
                    let parsed: Result<::models::IpamResponse, _> = serde_json::from_slice(&body);
                    parsed.map_err(|e| Error::from(e))
                }),
        )
    }
}
