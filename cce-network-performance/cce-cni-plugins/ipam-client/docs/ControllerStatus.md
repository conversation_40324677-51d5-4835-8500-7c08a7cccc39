# ControllerStatus

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Name of controller | [optional] [default to null]
**uuid** | **String** | UUID of controller | [optional] [default to null]
**configuration** | [***::models::ControllerStatusConfiguration**](ControllerStatus_configuration.md) |  | [optional] [default to null]
**status** | [***::models::ControllerStatusStatus**](ControllerStatus_status.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


