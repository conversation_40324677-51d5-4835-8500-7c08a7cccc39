# \MetricsApi

All URIs are relative to *https://localhost/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**metrics_get**](MetricsApi.md#metrics_get) | **Get** /metrics/ | Retrieve CCE metrics


# **metrics_get**
> Vec<::models::Metric> metrics_get()
Retrieve CCE metrics

### Required Parameters
This endpoint does not need any parameter.

### Return type

[**Vec<::models::Metric>**](Metric.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

