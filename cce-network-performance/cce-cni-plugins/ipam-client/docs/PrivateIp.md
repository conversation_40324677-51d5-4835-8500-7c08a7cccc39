# PrivateIp

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**public_ip_address** | **String** |  | [optional] [default to null]
**primary** | **bool** |  | [optional] [default to null]
**private_ip_address** | **String** |  | [optional] [default to null]
**subnet_id** | **String** | SubnetID subnet id of  private ip assigned with this. When allocating IP across subnets, the subnet of private IP may be different from ENI  | [optional] [default to null]
**gateway_ip** | **String** |  | [optional] [default to null]
**cidr_address** | **String** |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


