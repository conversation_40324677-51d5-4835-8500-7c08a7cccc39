# EndpointStatusChange

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**timestamp** | **String** | Timestamp when status change occurred | [optional] [default to null]
**code** | **String** | Code indicate type of status change | [optional] [default to null]
**message** | **String** | Status message | [optional] [default to null]
**state** | [***::models::EndpointState**](EndpointState.md) |  | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


