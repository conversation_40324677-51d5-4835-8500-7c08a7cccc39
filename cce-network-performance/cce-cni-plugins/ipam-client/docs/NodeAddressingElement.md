# NodeAddressingElement

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**enabled** | **bool** | True if address family is enabled | [optional] [default to null]
**ip** | **String** | IP address of node | [optional] [default to null]
**alloc_range** | **String** | Address pool to be used for local endpoints | [optional] [default to null]
**address_type** | **String** | Node address type, one of HostName, ExternalIP or InternalIP | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


