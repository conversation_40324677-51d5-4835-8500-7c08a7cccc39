# Metric

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Name of the metric | [optional] [default to null]
**value** | **f32** | Value of the metric | [optional] [default to null]
**labels** | **::std::collections::HashMap<String, String>** | Labels of the metric | [optional] [default to null]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


