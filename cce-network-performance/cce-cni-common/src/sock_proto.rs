/* Supported address families. */
pub const AF_UNSPEC: u32 = 0;
pub const AF_UNIX: u32 = 1; /* Unix domain sockets 		*/
pub const AF_LOCAL: u32 = 1; /* POSIX name for AF_UNIX	*/
pub const AF_INET: u32 = 2; /* Internet IP Protocol 	*/
pub const AF_AX25: u32 = 3; /* Amateur Radio AX.25 		*/
pub const AF_IPX: u32 = 4; /* Novell IPX 			*/
pub const AF_APPLETALK: u32 = 5; /* AppleTalk DDP 		*/
pub const AF_NETROM: u32 = 6; /* Amateur Radio NET/ROM 	*/
pub const AF_BRIDGE: u32 = 7; /* Multiprotocol bridge 	*/
pub const AF_ATMPVC: u32 = 8; /* ATM PVCs			*/
pub const AF_X25: u32 = 9; /* Reserved for X.25 project 	*/
pub const AF_INET6: u32 = 10; /* IP version 6			*/
pub const AF_ROSE: u32 = 11; /* Amateur Radio X.25 PLP	*/
pub const AF_DECnet: u32 = 12; /* Reserved for DECnet project	*/
pub const AF_NETBEUI: u32 = 13; /* Reserved for 802.2LLC project*/
pub const AF_SECURITY: u32 = 14; /* Security callback pseudo AF */
pub const AF_KEY: u32 = 15; /* PF_KEY key management API */
pub const AF_NETLINK: u32 = 16;
pub const AF_ROUTE: u32 = AF_NETLINK; /* Alias to emulate 4.4BSD */
pub const AF_PACKET: u32 = 17; /* Packet family		*/
pub const AF_ASH: u32 = 18; /* Ash				*/
pub const AF_ECONET: u32 = 19; /* Acorn Econet			*/
pub const AF_ATMSVC: u32 = 20; /* ATM SVCs			*/
pub const AF_RDS: u32 = 21; /* RDS sockets 			*/
pub const AF_SNA: u32 = 22; /* Linux SNA Project (nutters!) */
pub const AF_IRDA: u32 = 23; /* IRDA sockets			*/
pub const AF_PPPOX: u32 = 24; /* PPPoX sockets		*/
pub const AF_WANPIPE: u32 = 25; /* Wanpipe API Sockets */
pub const AF_LLC: u32 = 26; /* Linux LLC			*/
pub const AF_IB: u32 = 27; /* Native InfiniBand address	*/
pub const AF_MPLS: u32 = 28; /* MPLS */
pub const AF_CAN: u32 = 29; /* Controller Area Network      */
pub const AF_TIPC: u32 = 30; /* TIPC sockets			*/
pub const AF_BLUETOOTH: u32 = 31; /* Bluetooth sockets 		*/
pub const AF_IUCV: u32 = 32; /* IUCV sockets			*/
pub const AF_RXRPC: u32 = 33; /* RxRPC sockets 		*/
pub const AF_ISDN: u32 = 34; /* mISDN sockets 		*/
pub const AF_PHONET: u32 = 35; /* Phonet sockets		*/
pub const AF_IEEE802154: u32 = 36; /* IEEE802154 sockets		*/
pub const AF_CAIF: u32 = 37; /* CAIF sockets			*/
pub const AF_ALG: u32 = 38; /* Algorithm sockets		*/
pub const AF_NFC: u32 = 39; /* NFC sockets			*/
pub const AF_VSOCK: u32 = 40; /* vSockets			*/
pub const AF_KCM: u32 = 41; /* Kernel Connection Multiplexor*/
pub const AF_QIPCRTR: u32 = 42; /* Qualcomm IPC Router          */
pub const AF_SMC: u32 = 43; /* smc sockets: reserve number for
                             * PF_SMC protocol family that
                             * reuses AF_INET address family */
