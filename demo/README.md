# Demo Programs

这个目录包含两个演示程序：

## 1. Time Demo (main.go)

一个简单的Go程序，用于打印当前时间信息。

## 2. Logger Demo (logger-demo.go)

一个演示CCE网络项目中logger使用方式的程序，参考了 `cce-network-v2/plugins/roce/ipam_client.go` 中 `allocateRDMAIPsWithCCEAgent` 函数的logger使用方式。

## 功能特性

- 使用 logrus 进行格式化日志输出
- 使用 cobra 提供命令行界面
- 显示多种时间格式
- 支持版本信息查询
- 编译为Linux可执行文件

## 依赖

本项目使用了代码库中已有的依赖版本：

- Go 1.20
- github.com/sirupsen/logrus v1.8.1
- github.com/spf13/cobra v1.6.0
- github.com/google/uuid v1.3.0
- github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2 (本地模块)

## 编译

### 编译Time Demo

#### 编译为Linux可执行文件
```bash
cd demo
GOOS=linux GOARCH=amd64 go build -o time-demo-linux .
```

#### 编译为本地可执行文件
```bash
cd demo
go build -o time-demo .
```

### 编译Logger Demo

#### 编译为Linux可执行文件
```bash
cd demo
GOOS=linux GOARCH=amd64 go build -o logger-demo-linux logger-demo.go
```

#### 编译为本地可执行文件
```bash
cd demo
go build -o logger-demo-local logger-demo.go
```

## 使用方法

### Time Demo

#### 运行时间demo
```bash
./time-demo-linux
```

输出示例：
```
INFO[2025-08-01 16:13:13] Current time information                      timestamp=1754035993 timezone=Local weekday=Friday
=== Current Time Demo ===
Current time: 2025-08-01 16:13:13
RFC3339 format: 2025-08-01T16:13:13+08:00
Unix timestamp: 1754035993
Timezone: Local
Weekday: Friday
Year: 2025, Month: August, Day: 1
Hour: 16, Minute: 13, Second: 13
INFO[2025-08-01 16:13:13] Time demo completed successfully
```

#### 查看版本信息
```bash
./time-demo-linux version
```

#### 查看帮助信息
```bash
./time-demo-linux --help
```

### Logger Demo

#### 运行logger demo
```bash
./logger-demo-linux
```

输出示例：
```
level=info msg="====> Logger Demo Begins <====" component=demo plugin=logger-demo reqID=a2457d56-0530-4798-9e7d-9b783ea92cb2 version=1.0.0
level=info msg="Allocated RDMAIPAM for pod demo-namespace/demo-pod in ns /var/run/netns/demo-netns for container demo-container-12345" component=demo plugin=logger-demo reqID=a2457d56-0530-4798-9e7d-9b783ea92cb2 version=1.0.0
level=info msg="RDMA allocation simulation completed" component=demo containerID=demo-container-12345 k8sNamespace=demo-namespace k8sPodName=demo-pod netNSName=/var/run/netns/demo-netns plugin=logger-demo reqID=a2457d56-0530-4798-9e7d-9b783ea92cb2 version=1.0.0
...
```

#### 查看版本信息
```bash
./logger-demo-linux version
```

#### 查看帮助信息
```bash
./logger-demo-linux --help
```

## 文件说明

- `main.go` - Time Demo主程序文件
- `logger-demo.go` - Logger Demo主程序文件
- `go.mod` - Go模块依赖文件
- `time-demo-linux` - Time Demo编译后的Linux可执行文件
- `logger-demo-linux` - Logger Demo编译后的Linux可执行文件
- `README.md` - 本说明文件

## 特性说明

### Time Demo特性
程序会输出以下时间信息：
- 标准格式时间
- RFC3339格式时间
- Unix时间戳
- 时区信息
- 星期几
- 年、月、日详细信息
- 时、分、秒详细信息

同时使用logrus记录结构化日志，包含时间戳、时区和星期信息。

### Logger Demo特性
演示CCE网络项目中的日志系统使用方式：
- 使用 `logging.SetupCNILogging()` 设置syslog输出
- 使用 `logging.DefaultLogger.WithFields()` 创建结构化logger
- 模拟 `allocateRDMAIPsWithCCEAgent` 函数中的 `logger.Infof` 调用
- 使用 `logfields` 包中的常量进行标准化字段命名
- 演示不同日志级别（Debug, Info, Warn, Error）
- 演示结构化日志记录和时间相关的日志字段
- 每个日志条目都包含reqID、plugin、component等标识字段
