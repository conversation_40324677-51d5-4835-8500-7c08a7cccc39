# Time Demo

一个简单的Go程序，用于打印当前时间信息。

## 功能特性

- 使用 logrus 进行格式化日志输出
- 使用 cobra 提供命令行界面
- 显示多种时间格式
- 支持版本信息查询
- 编译为Linux可执行文件

## 依赖

本项目使用了代码库中已有的依赖版本：

- Go 1.20
- github.com/sirupsen/logrus v1.8.1
- github.com/spf13/cobra v1.6.0

## 编译

### 编译为Linux可执行文件

```bash
cd demo
GOOS=linux GOARCH=amd64 go build -o time-demo-linux .
```

### 编译为本地可执行文件

```bash
cd demo
go build -o time-demo .
```

## 使用方法

### 运行时间demo

```bash
./time-demo-linux
```

输出示例：
```
INFO[2025-08-01 16:13:13] Current time information                      timestamp=1754035993 timezone=Local weekday=Friday
=== Current Time Demo ===
Current time: 2025-08-01 16:13:13
RFC3339 format: 2025-08-01T16:13:13+08:00
Unix timestamp: 1754035993
Timezone: Local
Weekday: Friday
Year: 2025, Month: August, Day: 1
Hour: 16, Minute: 13, Second: 13
INFO[2025-08-01 16:13:13] Time demo completed successfully
```

### 查看版本信息

```bash
./time-demo-linux version
```

### 查看帮助信息

```bash
./time-demo-linux --help
```

## 文件说明

- `main.go` - 主程序文件
- `go.mod` - Go模块依赖文件
- `time-demo-linux` - 编译后的Linux可执行文件
- `README.md` - 本说明文件

## 特性说明

程序会输出以下时间信息：
- 标准格式时间
- RFC3339格式时间
- Unix时间戳
- 时区信息
- 星期几
- 年、月、日详细信息
- 时、分、秒详细信息

同时使用logrus记录结构化日志，包含时间戳、时区和星期信息。
