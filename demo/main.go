package main

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var (
	// 版本信息
	version = "1.0.0"
	
	// 根命令
	rootCmd = &cobra.Command{
		Use:   "time-demo",
		Short: "A demo application to print current time",
		Long:  `A simple demo application that prints the current time using logrus for formatted output.`,
		Run:   runTimeDemo,
	}
)

func init() {
	// 设置logrus格式
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		TimestampFormat: "2006-01-02 15:04:05",
	})
	
	// 添加版本命令
	rootCmd.AddCommand(&cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("time-demo version %s\n", version)
		},
	})
}

func runTimeDemo(cmd *cobra.Command, args []string) {
	// 获取当前时间
	now := time.Now()
	
	// 使用logrus输出格式化的时间信息
	logrus.WithFields(logrus.Fields{
		"timestamp": now.Unix(),
		"timezone":  now.Location().String(),
		"weekday":   now.Weekday().String(),
	}).Info("Current time information")
	
	// 输出多种时间格式
	fmt.Println("=== Current Time Demo ===")
	fmt.Printf("Current time: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("RFC3339 format: %s\n", now.Format(time.RFC3339))
	fmt.Printf("Unix timestamp: %d\n", now.Unix())
	fmt.Printf("Timezone: %s\n", now.Location().String())
	fmt.Printf("Weekday: %s\n", now.Weekday().String())
	fmt.Printf("Year: %d, Month: %s, Day: %d\n", now.Year(), now.Month().String(), now.Day())
	fmt.Printf("Hour: %d, Minute: %d, Second: %d\n", now.Hour(), now.Minute(), now.Second())
	
	// 使用logrus记录完成信息
	logrus.Info("Time demo completed successfully")
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		logrus.WithError(err).Fatal("Failed to execute command")
	}
}
