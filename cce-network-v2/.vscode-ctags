!_TAG_FILE_FORMAT	2	/extended format; --format=1 will not append ;" to lines/
!_TAG_FILE_SORTED	1	/0=unsorted, 1=sorted, 2=foldcase/
!_TAG_OUTPUT_MODE	u-ctags	/u-ctags or e-ctags/
!_TAG_PROGRAM_AUTHOR	Universal Ctags Team	//
!_TAG_PROGRAM_NAME	Universal Ctags	/Derived from Exuberant Ctags/
!_TAG_PROGRAM_URL	https://ctags.io/	/official site/
!_TAG_PROGRAM_VERSION	0.0.0	//
$(DOCKER_IMAGES)	Makefile	/^$(DOCKER_IMAGES):$/;"	t
$(SUBDIRS)	Makefile	/^$(SUBDIRS): force ## Execute default make target(make all) for the provided subdirectory.$/;"	t
$(TARGET)	cmd/Makefile	/^$(TARGET):$/;"	t
$(TARGET)	operator/Makefile	/^$(TARGET):$/;"	t
$(TARGETS)	cmd/Makefile	/^$(TARGETS):$/;"	t
$(TARGETS)	operator/Makefile	/^$(TARGETS):$/;"	t
BENCH	Makefile	/^BENCH ?= $(BENCH_EVAL)$/;"	m
BENCHFLAGS	Makefile	/^BENCHFLAGS ?= $(BENCHFLAGS_EVAL)$/;"	m
BENCHFLAGS_EVAL	Makefile	/^BENCHFLAGS_EVAL := -bench=$(BENCH) -run=^$ -benchtime=10s$/;"	m
BENCH_EVAL	Makefile	/^BENCH_EVAL := "."$/;"	m
CILIUM_BUILDER_IMAGE	api/v1/Makefile	/^CILIUM_BUILDER_IMAGE=registry.baidubce.com\/cce-plugin-dev\/cilium\/cilium-builder:12.0$/;"	m
CONTROLLER_GEN	Makefile	/^CONTROLLER_GEN = $(PWD)\/output\/bin\/controller-gen$/;"	m
DEEPEQUAL_GEN	Makefile	/^DEEPEQUAL_GEN = $(PWD)\/output\/bin\/deepequal-gen$/;"	m
DOCKER_IMAGES	Makefile	/^DOCKER_IMAGES := cce_ipam_agent cce_ipam_operator$/;"	m
DOCKER_REGISTRY	Makefile	/^kind-image: export DOCKER_REGISTRY=localhost:5000$/;"	m
DOCKER_REGISTRY	Makefile	/^microk8s: export DOCKER_REGISTRY=localhost:32000$/;"	m
GENERATE_BIN_DIR	Makefile	/^GENERATE_BIN_DIR = $(PWD)\/output\/bin$/;"	m
GENERATE_BIN_VERSION	Makefile	/^GENERATE_BIN_VERSION = v0.25.0$/;"	m
GIT_VERSION	Makefile	/^GIT_VERSION: force$/;"	t
GOFILES	Makefile	/^GOFILES ?= $(GOFILES_EVAL)$/;"	m
GOFILES_EVAL	Makefile	/^GOFILES_EVAL := $(subst _$(ROOT_DIR)\/,,$(shell $(GO_LIST) -find -e .\/...))$/;"	m
GOLANG_SRCFILES	Makefile	/^GOLANG_SRCFILES := $(shell for pkg in $(subst github.com\/baidubce\/baiducloud-cce-cni-driver\/c/;"	m
GOTEST_BASE	Makefile	/^GOTEST_BASE := -test.v -timeout 600s$/;"	m
GOTEST_UNIT_BASE	Makefile	/^GOTEST_UNIT_BASE := $(GOTEST_BASE) -check.vv$/;"	m
GO_IMAGE_VERSION	Makefile	/^GO_IMAGE_VERSION := $(shell awk -F. '{ z=$$3; if (z == "") z=0; print $$1 "." $$2 "." z}' GO_VER/;"	m
GO_INSTALLED_MAJOR_AND_MINOR_VERSION	Makefile	/^GO_INSTALLED_MAJOR_AND_MINOR_VERSION := $(shell $(GO) version | sed 's\/go version go\\([0-9]\\+/;"	m
GO_MAJOR_AND_MINOR_VERSION	Makefile	/^GO_MAJOR_AND_MINOR_VERSION := $(shell sed 's\/\\([0-9]\\+\\).\\([0-9]\\+\\)\\(.[0-9]\\+\\)\\?\/\\/;"	m
GO_MODULE_PATH	Makefile	/^GO_MODULE_PATH = $(GOPATH)\/pkg\/mod$/;"	m
GO_VERSION	Makefile	/^GO_VERSION := $(shell go version > GO_VERSION && cat GO_VERSION)$/;"	m
JOB_BASE_NAME	Makefile	/^JOB_BASE_NAME ?= cce_test$/;"	m
LOCAL_AGENT_IMAGE	Makefile	/^kind-image: export LOCAL_AGENT_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/cce-dev:$(LOCAL_/;"	m
LOCAL_AGENT_IMAGE	Makefile	/^microk8s: export LOCAL_AGENT_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/cce-dev:$(LOCAL_IM/;"	m
LOCAL_IMAGE_TAG	Makefile	/^LOCAL_IMAGE_TAG=local$/;"	m
LOCAL_OPERATOR_IMAGE	Makefile	/^kind-image: export LOCAL_OPERATOR_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/operator:$(LO/;"	m
LOCAL_OPERATOR_IMAGE	Makefile	/^microk8s: export LOCAL_OPERATOR_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/operator:$(LOCA/;"	m
LOGLEVEL	Makefile	/^LOGLEVEL ?= "error"$/;"	m
MOCKGEN	Makefile	/^MOCKGEN = $(PWD)\/output\/bin\/mockgen$/;"	m
NOOPT	Makefile	/^debug: export NOOPT=1 ## Builds CCE by disabling inlining, compiler optimizations and without st/;"	m
NOSTRIP	Makefile	/^debug: export NOSTRIP=1$/;"	m
PRIV_TEST_PKGS	Makefile	/^PRIV_TEST_PKGS ?= $(PRIV_TEST_PKGS_EVAL)$/;"	m
PRIV_TEST_PKGS_EVAL	Makefile	/^PRIV_TEST_PKGS_EVAL := $(shell echo $(PRIV_TEST_PKGS_FILTER) | xargs dirname | sort | uniq | gre/;"	m
PRIV_TEST_PKGS_FILTER	Makefile	/^PRIV_TEST_PKGS_FILTER := $(shell for pkg in $(TESTPKGS); do echo $$pkg; done | xargs grep --incl/;"	m
SKIP_CUSTOMVET_CHECK	Makefile	/^SKIP_CUSTOMVET_CHECK ?= "false"$/;"	m
SKIP_K8S_CODE_GEN_CHECK	Makefile	/^SKIP_K8S_CODE_GEN_CHECK ?= "true"$/;"	m
SKIP_KVSTORES	Makefile	/^SKIP_KVSTORES ?= "false"$/;"	m
SKIP_VET	Makefile	/^SKIP_VET ?= "false"$/;"	m
SUBDIRS	Makefile	/^SUBDIRS := operator cmd$/;"	m
SUBDIRS_CILIUM_CONTAINER	Makefile	/^SUBDIRS_CILIUM_CONTAINER := proxylib envoy bpf cce daemon cce-health bugtool tools\/mount tools\//;"	m
SWAGGER	Makefile	/^SWAGGER := $(CONTAINER_ENGINE) run -u $(shell id -u):$(shell id -g) --rm -v $(CURDIR):$(CURDIR) /;"	m
SWAGGER_VERSION	Makefile	/^SWAGGER_VERSION := v0.25.0$/;"	m
TARGETS	cmd/Makefile	/^TARGETS := agent pcb-ipam webhook$/;"	m
TARGETS	operator/Makefile	/^TARGETS := cce-ipam-operator cce-ipam-operator-generic cce-ipam-operator-pcb$/;"	m
TEST_LDFLAGS	Makefile	/^TEST_LDFLAGS=-ldflags "-X github.com\/baidubce\/baiducloud-cce-cni-driver\/cce-ipam-v2\/pkg\/kvs/;"	m
TEST_UNITTEST_LDFLAGS	Makefile	/^TEST_UNITTEST_LDFLAGS=-ldflags "-X github.com\/baidubce\/baiducloud-cce-cni-driver\/cce-ipam-v2\//;"	m
all	Makefile	/^all: precheck build postcheck ## Default make target that perform precheck -> build -> postcheck$/;"	t
all	cmd/Makefile	/^all: $(TARGETS)$/;"	t
all	operator/Makefile	/^all: $(TARGETS)$/;"	t
base_package	Makefile	/^base_package = github.com\/baidubce\/baiducloud-cce-cni-driver\/cce-ipam-v2$/;"	m
build	Makefile	/^build: $(SUBDIRS) ## Builds all the components for CCE by executing make in the respective sub d/;"	t
build-container	Makefile	/^build-container: ## Builds components required for cce-agent container.$/;"	t
cce-ipam-operator	operator/Makefile	/^cce-ipam-operator: GO_TAGS_FLAGS+=ipam_provider_operator,ipam_provider_private_cloud_base$/;"	t
cce-ipam-operator-generic	operator/Makefile	/^cce-ipam-operator-generic: GO_TAGS_FLAGS+=ipam_provider_operator$/;"	t
cce-ipam-operator-pcb	operator/Makefile	/^cce-ipam-operator-pcb: GO_TAGS_FLAGS+=ipam_provider_private_cloud_base$/;"	t
check-go-version	Makefile	/^check-go-version: ## Check locally install Go version against required Go version.$/;"	t
check-microk8s	Makefile	/^check-microk8s: ## Validate if microk8s is ready to install cce.$/;"	t
clean	Makefile	/^clean: ## Perform overall cleanup for CCE.$/;"	t
clean	cmd/Makefile	/^clean:$/;"	t
clean	operator/Makefile	/^clean:$/;"	t
clean-container	Makefile	/^clean-container: ## Perform `make clean` for each component required in cce-agent container.$/;"	t
clean-tags	Makefile	/^clean-tags: ## Remove all the tags files from the repository.$/;"	t
cscope.files	Makefile	/^cscope.files: $(GOLANG_SRCFILES) $(BPF_SRCFILES) ## Generate cscope.files with the list of all f/;"	t
debug	Makefile	/^debug: all$/;"	t
debug	Makefile	/^debug: export NOOPT=1 ## Builds CCE by disabling inlining, compiler optimizations and without st/;"	t
debug	Makefile	/^debug: export NOSTRIP=1$/;"	t
dev-doctor	Makefile	/^dev-doctor: ## Run CCE dev-doctor to validate local development environment.$/;"	t
docker	Makefile	/^docker: $(DOCKER_IMAGES)$/;"	t
force	Makefile	/^force :;$/;"	t
generate-api	Makefile	/^generate-api: api\/v1\/openapi.yaml ## Generate cce-agent client, model and server code from ope/;"	t
generate-cov	Makefile	/^generate-cov: ## Generate coverage report for CCE integration-tests.$/;"	t
generate-health-api	Makefile	/^generate-health-api: api\/v1\/health\/openapi.yaml ## Generate cce-health client, model and serv/;"	t
generate-k8s-api	Makefile	/^generate-k8s-api: ## Generate CCE k8s API client, deepcopy and deepequal Go sources.$/;"	t
generate-mock	Makefile	/^generate-mock:$/;"	t
generate-operator-api	Makefile	/^generate-operator-api: api\/v1\/operator\/openapi.yaml ## Generate cce-operator client, model an/;"	t
generate_deepequal	Makefile	/^define generate_deepequal$/;"	m
generate_k8s_api	Makefile	/^define generate_k8s_api$/;"	m
generate_k8s_api_all	Makefile	/^define generate_k8s_api_all$/;"	m
generate_k8s_api_deepcopy_deepequal	Makefile	/^define generate_k8s_api_deepcopy_deepequal$/;"	m
generate_k8s_api_deepcopy_deepequal_client	Makefile	/^define generate_k8s_api_deepcopy_deepequal_client$/;"	m
generate_k8s_protobuf	Makefile	/^define generate_k8s_protobuf$/;"	m
generate_mock	Makefile	/^define generate_mock$/;"	m
go-get-tool	Makefile	/^define go-get-tool$/;"	m
gofmt	Makefile	/^gofmt: ## Run gofmt on Go source files in the repository.$/;"	t
golangci-lint	Makefile	/^golangci-lint: ## Run golangci-lint$/;"	t
govet	Makefile	/^govet: ## Run govet on Go source files in the repository.$/;"	t
help	Makefile	/^help: ## Display help for the Makefile, from https:\/\/www.thapaliya.com\/en\/writings\/well-doc/;"	t
init-coverage	Makefile	/^init-coverage: ## Initialize converage report for CCE integration-tests.$/;"	t
install	Makefile	/^install:$/;"	t
install	cmd/Makefile	/^install:$/;"	t
install	operator/Makefile	/^install:$/;"	t
install-manpages	Makefile	/^install-manpages: ## Install manpages the CCE CLI.$/;"	t
integration-tests	Makefile	/^integration-tests: ## Runs all integration-tests for CCE.$/;"	t
integration-tests	Makefile	/^integration-tests: GO_TAGS_FLAGS+=integration_tests$/;"	t
kind	Makefile	/^kind: ## Create a kind cluster for CCE development.$/;"	t
kind-down	Makefile	/^kind-down: ## Destroy a kind cluster for CCE development.$/;"	t
kind-image	Makefile	/^kind-image: ## Build cce-dev docker image and import it into kind.$/;"	t
kind-image	Makefile	/^kind-image: export DOCKER_REGISTRY=localhost:5000$/;"	t
kind-image	Makefile	/^kind-image: export LOCAL_AGENT_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/cce-dev:$(LOCAL_/;"	t
kind-image	Makefile	/^kind-image: export LOCAL_OPERATOR_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/operator:$(LO/;"	t
licenses-all	Makefile	/^licenses-all: ## Generate file with all the License from dependencies.$/;"	t
lint	Makefile	/^lint: golangci-lint bpf-mock-lint ## Run golangci-lint and bpf-mock linters.$/;"	t
logging-subsys-field	Makefile	/^logging-subsys-field: ## Validate logrus subsystem field for logs in Go source code.$/;"	t
manifests	Makefile	/^manifests: ## Generate K8s manifests e.g. CRD, RBAC etc.$/;"	t
manpages	Makefile	/^manpages: ## Generate manpage for CCE CLI.$/;"	t
microk8s	Makefile	/^microk8s: check-microk8s ## Build cce-dev docker image and import to microk8s$/;"	t
microk8s	Makefile	/^microk8s: export DOCKER_REGISTRY=localhost:32000$/;"	t
microk8s	Makefile	/^microk8s: export LOCAL_AGENT_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/cce-dev:$(LOCAL_IM/;"	t
microk8s	Makefile	/^microk8s: export LOCAL_OPERATOR_IMAGE=$(DOCKER_REGISTRY)\/$(DOCKER_DEV_ACCOUNT)\/operator:$(LOCA/;"	t
postcheck	Makefile	/^postcheck: build ## Run CCE build postcheck (update-cmdref, build documentation etc.).$/;"	t
pprof-block	Makefile	/^pprof-block: ## Get Go pprof block profile.$/;"	t
pprof-heap	Makefile	/^pprof-heap: ## Get Go pprof heap profile.$/;"	t
pprof-mutex	Makefile	/^pprof-mutex: ## Get Go pprof mutex profile.$/;"	t
pprof-profile	Makefile	/^pprof-profile: ## Get Go pprof profile.$/;"	t
pprof-trace-5s	Makefile	/^pprof-trace-5s: ## Get Go pprof trace for a duration of 5 seconds.$/;"	t
precheck	Makefile	/^precheck: check-go-version logging-subsys-field ## Peform build precheck for the source code.$/;"	t
proto	api/v1/Makefile	/^proto:$/;"	t
release	Makefile	/^release: ## Perform a Git release for CCE.$/;"	t
render-docs	Makefile	/^render-docs: ## Run server with live preview to render documentation.$/;"	t
tags	Makefile	/^tags: $(GOLANG_SRCFILES) $(BPF_SRCFILES) cscope.files ## Generate tags for Go and BPF source fil/;"	t
test-docs	Makefile	/^test-docs: ## Build HTML documentation.$/;"	t
tests-privileged	Makefile	/^tests-privileged: GO_TAGS_FLAGS+=privileged_tests ## Run integration-tests for CCE that requires/;"	t
tests-privileged	Makefile	/^tests-privileged:$/;"	t
update-authors	Makefile	/^update-authors: ## Update AUTHORS file for CCE repository.$/;"	t
update-go-version	Makefile	/^update-go-version: ## Update Go version for all the components (images, CI, dev-doctor etc.).$/;"	t
veryclean	Makefile	/^veryclean: ## Perform complete cleanup for container engine images(including build cache).$/;"	t
