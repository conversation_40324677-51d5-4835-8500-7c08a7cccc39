/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package logging

import (
	"io"
	"os"

	"github.com/sirupsen/logrus"
	flag "github.com/spf13/pflag"
	"github.com/spf13/viper"
)

func init() {
	flags := flag.NewFlagSet("init-debug", flag.ContinueOnError)
	flags.Usage = func() {}
	flags.SetOutput(io.Discard)

	debug := flags.Bool("debug", false, "")
	flags.Parse(os.Args)

	if *debug || viper.GetBool("debug") {
		DefaultLogger.SetLevel(logrus.DebugLevel)
		//DefaultLogger.SetReportCaller(true)
	}
}
