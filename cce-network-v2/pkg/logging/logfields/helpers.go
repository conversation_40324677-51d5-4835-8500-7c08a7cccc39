/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package logfields

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
)

// Repr formats an object with the Printf %+v formatter
func Repr(s interface{}) string {
	return fmt.Sprintf("%+v", s)
}

func Json(s interface{}) string {
	b, e := json.Marshal(s)
	if e != nil {
		return ""
	}
	return string(b)
}

func NewContext() context.Context {
	return WithtTraceID(context.TODO())
}

func WithtTraceID(ctx context.Context) context.Context {
	traceID := GetUUID()
	ctx = context.WithValue(ctx, TraceID, traceID)
	return ctx
}

// GetUUID generates a uuid V4 with error and context concern
func GetUUID() string {
	return uuid.New().String()
}
