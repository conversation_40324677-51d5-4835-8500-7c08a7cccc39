/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package addressing

// AddressType represents a type of IP address for a node. They are copied
// from k8s.io/api/core/v1/types.go to avoid pulling in a lot of Kubernetes
// imports into this package.s
type AddressType string

const (
	NodeHostName      AddressType = "Hostname"
	NodeExternalIP    AddressType = "ExternalIP"
	NodeInternalIP    AddressType = "InternalIP"
	NodeExternalDNS   AddressType = "ExternalDNS"
	NodeInternalDNS   AddressType = "InternalDNS"
	NodeCCEInternalIP AddressType = "CCEInternalIP"
)
