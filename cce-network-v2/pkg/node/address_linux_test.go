//go:build !darwin && privileged_tests

/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package node

import (
	"fmt"
	"net"
	"testing"

	"github.com/vishvananda/netlink"
	. "gopkg.in/check.v1"
)

func Test(t *testing.T) {
	TestingT(t)
}

type NodePrivilegedSuite struct{}

var _ = Suite(&NodePrivilegedSuite{})

func (s *NodePrivilegedSuite) SetUpTest(c *C) {

}

func (s *NodePrivilegedSuite) Test_firstGlobalV4Addr(c *C) {
	testCases := []struct {
		name           string
		ipsOnInterface []string
		preferredIP    string
		preferPublic   bool
		want           string
	}{
		{
			name:           "public IP preferred by default",
			ipsOnInterface: []string{"***********", "********"},
			want:           "********",
		},
		{
			name:           "prefer IP when not preferPublic",
			ipsOnInterface: []string{"***********", "********"},
			preferredIP:    "***********",
			want:           "***********",
		},
		{
			name:           "preferPublic when not prefer IP",
			ipsOnInterface: []string{"***********", "********"},
			preferPublic:   true,
			want:           "********",
		},
		{
			name:           "preferPublic when prefer IP",
			ipsOnInterface: []string{"***********", "********"},
			preferPublic:   true,
			preferredIP:    "***********",
			want:           "********",
		},
	}
	const ifName = "dummy_iface"
	for _, tc := range testCases {
		err := setupDummyDevice(ifName, tc.ipsOnInterface...)
		c.Assert(err, IsNil)

		got, err := firstGlobalV4Addr(ifName, net.ParseIP(tc.preferredIP), tc.preferPublic)
		if err != nil {
			c.Error(err)
		} else {
			c.Check(tc.want, Equals, got.String())
		}
		removeDevice(ifName)
	}
}

func setupDummyDevice(name string, ips ...string) error {
	dummy := &netlink.Dummy{
		LinkAttrs: netlink.LinkAttrs{
			Name: name,
		},
	}
	if err := netlink.LinkAdd(dummy); err != nil {
		return fmt.Errorf("netlink.LinkAdd failed: %v", err)
	}

	if err := netlink.LinkSetUp(dummy); err != nil {
		removeDevice(name)
		return fmt.Errorf("netlink.LinkSetUp failed: %v", err)
	}

	for _, ipStr := range ips {
		ip := net.ParseIP(ipStr)
		if ip == nil || ip.To4() == nil {
			removeDevice(name)
			return fmt.Errorf("invalid ipv4 IP : %v", ipStr)
		}
		ipnet := &net.IPNet{IP: ip, Mask: net.CIDRMask(32, 32)}
		addr := &netlink.Addr{IPNet: ipnet}
		if err := netlink.AddrAdd(dummy, addr); err != nil {
			removeDevice(name)
			return err
		}
	}

	return nil
}

func removeDevice(name string) {
	l, err := netlink.LinkByName(name)
	if err == nil {
		netlink.LinkDel(l)
	}
}
