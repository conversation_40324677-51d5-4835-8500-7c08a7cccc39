//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

// Code generated by deepcopy-gen. DO NOT EDIT.

package types

import (
	net "net"

	cidr "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/cidr"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Address) DeepCopyInto(out *Address) {
	*out = *in
	if in.IP != nil {
		in, out := &in.IP, &out.IP
		*out = make(net.IP, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Address.
func (in *Address) DeepCopy() *Address {
	if in == nil {
		return nil
	}
	out := new(Address)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Node) DeepCopyInto(out *Node) {
	*out = *in
	if in.IPAddresses != nil {
		in, out := &in.IPAddresses, &out.IPAddresses
		*out = make([]Address, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.IPv4AllocCIDR != nil {
		in, out := &in.IPv4AllocCIDR, &out.IPv4AllocCIDR
		*out = (*in).DeepCopy()
	}
	if in.IPv4SecondaryAllocCIDRs != nil {
		in, out := &in.IPv4SecondaryAllocCIDRs, &out.IPv4SecondaryAllocCIDRs
		*out = make([]*cidr.CIDR, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = (*in).DeepCopy()
			}
		}
	}
	if in.IPv6AllocCIDR != nil {
		in, out := &in.IPv6AllocCIDR, &out.IPv6AllocCIDR
		*out = (*in).DeepCopy()
	}
	if in.IPv6SecondaryAllocCIDRs != nil {
		in, out := &in.IPv6SecondaryAllocCIDRs, &out.IPv6SecondaryAllocCIDRs
		*out = make([]*cidr.CIDR, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = (*in).DeepCopy()
			}
		}
	}
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Node.
func (in *Node) DeepCopy() *Node {
	if in == nil {
		return nil
	}
	out := new(Node)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RegisterNode) DeepCopyInto(out *RegisterNode) {
	*out = *in
	in.Node.DeepCopyInto(&out.Node)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RegisterNode.
func (in *RegisterNode) DeepCopy() *RegisterNode {
	if in == nil {
		return nil
	}
	out := new(RegisterNode)
	in.DeepCopyInto(out)
	return out
}
