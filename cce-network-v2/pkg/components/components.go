/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package components

import (
	"os"
	"strings"
)

const (
	// CCEAgentName is the name of cce-agent (daemon) process name.
	CCEAgentName = "cce-network-agent"
	// CCEOperatortName is the name of cce-operator process name.
	CCEOperatortName = "cce-network-operator"
	// CCEDaemonTestName is the name of test binary for daemon package.
	CCEDaemonTestName = "cmd.test"
)

// IsCCEAgent checks whether the current process is cce-agent (daemon).
func IsCCEAgent() bool {
	binaryName := os.Args[0]
	return strings.HasSuffix(binaryName, CCEAgentName) ||
		strings.HasSuffix(binaryName, CCEDaemonTestName)
}
