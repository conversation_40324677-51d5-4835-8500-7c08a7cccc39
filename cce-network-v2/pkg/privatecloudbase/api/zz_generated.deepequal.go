//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

// Code generated by deepequal-gen. DO NOT EDIT.

package api

// DeepEqual is an autogenerated deepequal function, deeply comparing the
// receiver with other. in must be non-nil.
func (in *Subnet) DeepEqual(other *Subnet) bool {
	if other == nil {
		return false
	}

	if in.Name != other.Name {
		return false
	}
	if in.Region != other.Region {
		return false
	}
	if in.VPC != other.VPC {
		return false
	}
	if in.Purpose != other.Purpose {
		return false
	}
	if in.ISP != other.ISP {
		return false
	}
	if in.IPVersion != other.IPVersion {
		return false
	}
	if in.RangeStart != other.RangeStart {
		return false
	}
	if in.RangeEnd != other.RangeEnd {
		return false
	}
	if in.Excludes != other.Excludes {
		return false
	}
	if in.CIDR != other.CIDR {
		return false
	}
	if in.Gateway != other.Gateway {
		return false
	}
	if in.NatGW != other.NatGW {
		return false
	}
	if in.VlanID != other.VlanID {
		return false
	}
	if in.Priority != other.Priority {
		return false
	}
	if in.Enable != other.Enable {
		return false
	}

	return true
}
