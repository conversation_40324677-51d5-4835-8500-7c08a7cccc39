// /*
//  * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
//  *
//  * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
//  * except in compliance with the License. You may obtain a copy of the License at
//  *
//  * http://www.apache.org/licenses/LICENSE-2.0
//  *
//  * Unless required by applicable law or agreed to in writing, software distributed under the
//  * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
//  * either express or implied. See the License for the specific language governing permissions
//  * and limitations under the License.
//  *
//  */
//
//

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/privatecloudbase/api (interfaces: Client)

// Package testing is a generated GoMock package.
package testing

import (
	context "context"
	reflect "reflect"

	api "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/privatecloudbase/api"
	gomock "github.com/golang/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// AcquireIPBySubnet mocks base method.
func (m *MockClient) AcquireIPBySubnet(arg0 context.Context, arg1 api.AcquireIPBySubnetRequest) (*api.AllocatedIPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireIPBySubnet", arg0, arg1)
	ret0, _ := ret[0].(*api.AllocatedIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireIPBySubnet indicates an expected call of AcquireIPBySubnet.
func (mr *MockClientMockRecorder) AcquireIPBySubnet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireIPBySubnet", reflect.TypeOf((*MockClient)(nil).AcquireIPBySubnet), arg0, arg1)
}

// AllocatedIPs mocks base method.
func (m *MockClient) AllocatedIPs(arg0 context.Context) (*api.ListAllocatedIPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocatedIPs", arg0)
	ret0, _ := ret[0].(*api.ListAllocatedIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocatedIPs indicates an expected call of AllocatedIPs.
func (mr *MockClientMockRecorder) AllocatedIPs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocatedIPs", reflect.TypeOf((*MockClient)(nil).AllocatedIPs), arg0)
}

// BatchAcquireIPBySubnet mocks base method.
func (m *MockClient) BatchAcquireIPBySubnet(arg0 context.Context, arg1 api.BatchAcquireIPBySubnetRequest) (*api.ListAllocatedIPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAcquireIPBySubnet", arg0, arg1)
	ret0, _ := ret[0].(*api.ListAllocatedIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAcquireIPBySubnet indicates an expected call of BatchAcquireIPBySubnet.
func (mr *MockClientMockRecorder) BatchAcquireIPBySubnet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAcquireIPBySubnet", reflect.TypeOf((*MockClient)(nil).BatchAcquireIPBySubnet), arg0, arg1)
}

// BatchReleaseIP mocks base method.
func (m *MockClient) BatchReleaseIP(arg0 context.Context, arg1 api.BatchReleaseIPRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchReleaseIP", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchReleaseIP indicates an expected call of BatchReleaseIP.
func (mr *MockClientMockRecorder) BatchReleaseIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchReleaseIP", reflect.TypeOf((*MockClient)(nil).BatchReleaseIP), arg0, arg1)
}

// CreateSubnet mocks base method.
func (m *MockClient) CreateSubnet(arg0 context.Context, arg1 api.Subnet) (*api.SubnetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubnet", arg0, arg1)
	ret0, _ := ret[0].(*api.SubnetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubnet indicates an expected call of CreateSubnet.
func (mr *MockClientMockRecorder) CreateSubnet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubnet", reflect.TypeOf((*MockClient)(nil).CreateSubnet), arg0, arg1)
}

// ListSubnets mocks base method.
func (m *MockClient) ListSubnets(arg0 context.Context) (*api.ListSubnetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSubnets", arg0)
	ret0, _ := ret[0].(*api.ListSubnetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSubnets indicates an expected call of ListSubnets.
func (mr *MockClientMockRecorder) ListSubnets(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSubnets", reflect.TypeOf((*MockClient)(nil).ListSubnets), arg0)
}
