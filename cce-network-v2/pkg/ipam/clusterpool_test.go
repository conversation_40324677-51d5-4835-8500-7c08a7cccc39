// SPDX-License-Identifier: Apache-2.0
// Copyright 2021 Authors of CCE

//go:build !privileged_tests

package ipam

import (
	"context"
	"fmt"
	"net"
	"testing"
	"time"

	. "github.com/onsi/gomega"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/cidr"
	ipamOption "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/ipam/option"
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/ipam/types"
	ccev2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/watchers/subscriber"
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/lock"
)

// testConfigurationClusterPool supports both IPv4 and IPv6 based on the family parameter
type testConfigurationClusterPool struct {
	family Family
}

func (t *testConfigurationClusterPool) IPv4Enabled() bool {
	return t.family == IPv4
}

func (t *testConfigurationClusterPool) IPv6Enabled() bool {
	return t.family == IPv6
}

func (t *testConfigurationClusterPool) HealthCheckingEnabled() bool {
	return true
}

func (t *testConfigurationClusterPool) UnreachableRoutesEnabled() bool {
	return false
}

func (t *testConfigurationClusterPool) IPAMMode() string {
	return ipamOption.IPAMClusterPool
}

func (t *testConfigurationClusterPool) SetIPv4NativeRoutingCIDR(cidr *cidr.CIDR) {}

func (t *testConfigurationClusterPool) GetIPv4NativeRoutingCIDR() *cidr.CIDR {
	return nil
}

func (t *testConfigurationClusterPool) GetCCEEndpointGC() time.Duration {
	return 0
}

func (t *testConfigurationClusterPool) GetFixedIPTimeout() time.Duration {
	return 0
}

type fakeK8sNetResourceSetAPI struct {
	mutex lock.Mutex
	node  *ccev2.NetResourceSet
	sub   subscriber.NetResourceSet

	onUpsertEvent func()
	onDeleteEvent func()
}

func (f *fakeK8sNetResourceSetAPI) RegisterNetResourceSetSubscriber(s subscriber.NetResourceSet) {
	f.sub = s
}

// UpdateStatus implements nodeUpdater
func (f *fakeK8sNetResourceSetAPI) UpdateStatus(_ context.Context, netResourceSet *ccev2.NetResourceSet, _ v1.UpdateOptions) (*ccev2.NetResourceSet, error) {
	err := f.updateNode(netResourceSet)
	return netResourceSet, err
}

// currentNode returns a the current snapshot of the node
func (f *fakeK8sNetResourceSetAPI) currentNode() *ccev2.NetResourceSet {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	return f.node.DeepCopy()
}

// updateNode is to be invoked by the test code to simulate updates by the operator
func (f *fakeK8sNetResourceSetAPI) updateNode(newNode *ccev2.NetResourceSet) error {
	f.mutex.Lock()
	oldNode := f.node
	if oldNode == nil {
		f.mutex.Unlock()
		return fmt.Errorf("failed to update NetResourceSet %q: node not found", newNode.Name)
	}
	f.node = newNode

	sub := f.sub
	onUpsertEvent := f.onUpsertEvent
	f.mutex.Unlock()

	var err error
	if sub != nil {
		err = sub.OnUpdateNetResourceSet(oldNode, newNode, nil)
	}
	if onUpsertEvent != nil {
		onUpsertEvent()
	}

	return err
}

// deleteNode is to be invoked by the test code to simulate an unexpected node deletion
func (f *fakeK8sNetResourceSetAPI) deleteNode() error {
	f.mutex.Lock()
	oldNode := f.node
	f.node = nil

	sub := f.sub
	onDeleteEvent := f.onDeleteEvent
	f.mutex.Unlock()

	var err error
	if sub != nil {
		err = sub.OnDeleteNetResourceSet(oldNode, nil)
	}
	if onDeleteEvent != nil {
		onDeleteEvent()
	}
	return err
}

func TestPodCIDRPool(t *testing.T) {
	for _, tc := range []struct {
		family       Family
		podCIDR      string
		capacity     int
		inRangeIP    net.IP
		outOfRangeIP net.IP
	}{
		{
			family:       IPv4,
			podCIDR:      "***********/27",
			capacity:     30,
			inRangeIP:    net.ParseIP("***********"),
			outOfRangeIP: net.ParseIP("********"),
		},
		{
			family:       IPv6,
			podCIDR:      "1::/123",
			capacity:     30,
			inRangeIP:    net.ParseIP("1::1"),
			outOfRangeIP: net.ParseIP("2::1"),
		},
	} {
		t.Run(string(tc.family), func(t *testing.T) {
			RegisterTestingT(t)

			p := newPodCIDRPool(0, 0, nil)

			// Test behavior when empty.
			ip, err := p.allocateNext()
			Expect(err).To(HaveOccurred())
			Expect(ip).To(BeNil())
			ipToOwner, usedIPs, availableIPs, numPodCIDRs, err := p.dump()
			Expect(err).ToNot(HaveOccurred())
			Expect(ipToOwner).To(BeEmpty())
			Expect(usedIPs).To(BeZero())
			Expect(availableIPs).To(BeZero())
			Expect(numPodCIDRs).To(BeZero())
			Expect(p.hasAvailableIPs()).To(BeFalse())
			Expect(p.status()).To(Equal(types.PodCIDRMap{}))

			// Add pod CIDRs.
			p.updatePool([]string{tc.podCIDR})
			Expect(p.hasAvailableIPs()).To(BeTrue())
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR: {
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Test allocating a fixed IP.
			Expect(p.allocate(tc.inRangeIP)).To(Succeed())
			ipToOwner, usedIPs, availableIPs, numPodCIDRs, err = p.dump()
			Expect(err).ToNot(HaveOccurred())
			Expect(ipToOwner).To(Equal(map[string]string{
				tc.inRangeIP.String(): "",
			}))
			Expect(usedIPs).To(Equal(1))
			Expect(availableIPs).To(Equal(tc.capacity - 1))
			Expect(numPodCIDRs).To(Equal(1))
			Expect(p.release(tc.inRangeIP)).To(Succeed())

			// Test allocating an out-of-range IP.
			Expect(p.allocate(tc.outOfRangeIP)).ShouldNot(Succeed())
			Expect(p.release(tc.outOfRangeIP)).To(Succeed())

			// Test allocation of all IPs.
			ips := allocateNextN(p, tc.capacity, nil)

			// Test behavior when full.
			Expect(p.hasAvailableIPs()).To(BeFalse())
			ip, err = p.allocateNext()
			Expect(err).To(HaveOccurred())
			Expect(ip).To(BeNil())
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR: {
					Status: types.PodCIDRStatusDepleted,
				},
			}))

			// Test release of all IPs.
			for i, ip := range ips {
				Expect(p.release(ip)).To(Succeed())
				Expect(p.hasAvailableIPs()).To(BeTrue())
				expectedStatus := types.PodCIDRStatusInUse
				if i+1 < p.allocationThreshold {
					expectedStatus = types.PodCIDRStatusDepleted
				}
				Expect(p.status()).To(Equal(types.PodCIDRMap{
					tc.podCIDR: {
						Status: expectedStatus,
					},
				}))
			}

			// Test release of all pod CIDRs.
			p.updatePool(nil)
			ip, err = p.allocateNext()
			Expect(err).To(HaveOccurred())
			Expect(ip).To(BeNil())
			Expect(p.hasAvailableIPs()).To(BeFalse())
			Expect(p.status()).To(Equal(types.PodCIDRMap{}))
		})
	}
}

func TestPodCIDRPoolTwoPools(t *testing.T) {
	for _, tc := range []struct {
		family    Family
		podCIDR1  string
		capacity1 int
		podCIDR2  string
		capacity2 int
	}{
		{
			family:    IPv4,
			podCIDR1:  "***********/27",
			capacity1: 30,
			podCIDR2:  "10.0.0.0/27",
			capacity2: 30,
		},
		{
			family:    IPv6,
			podCIDR1:  "1::/123",
			capacity1: 30,
			podCIDR2:  "2::/123",
			capacity2: 30,
		},
	} {
		t.Run(string(tc.family), func(t *testing.T) {
			RegisterTestingT(t)

			p := newPodCIDRPool(0, 0, nil)
			p.updatePool([]string{tc.podCIDR1})

			// Test behavior with no allocations.
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusInUse,
				},
			}))

			_, podCIDR1, err := net.ParseCIDR(tc.podCIDR1)
			Expect(err).ToNot(HaveOccurred())
			_, podCIDR2, err := net.ParseCIDR(tc.podCIDR2)
			Expect(err).ToNot(HaveOccurred())

			// Test allocation and release of a single IP.
			ip, err := p.allocateNext()
			Expect(err).ToNot(HaveOccurred())
			Expect(ip).ToNot(BeNil())
			Expect(podCIDR1.Contains(ip)).To(BeTrue())
			Expect(p.release(ip)).To(Succeed())

			// Test fully allocating the first pod CIDR.
			ips1 := allocateNextN(p, tc.capacity1, podCIDR1)
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusDepleted,
				},
			}))

			// Allocate the second pod CIDR.
			p.updatePool([]string{tc.podCIDR1, tc.podCIDR2})
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: {
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Test fully allocating the second pod CIDR.
			ips2 := allocateNextN(p, tc.capacity2, podCIDR2)
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: {
					Status: types.PodCIDRStatusDepleted,
				},
			}))

			// Test that IP addresses are allocated from the first pod CIDR by
			// preference.
			Expect(p.release(ips1[0])).To(Succeed())
			Expect(p.release(ips2[0])).To(Succeed())
			ip, err = p.allocateNext()
			Expect(err).ToNot(HaveOccurred())
			Expect(ip).To(Equal(ips1[0]))
			ip, err = p.allocateNext()
			Expect(err).ToNot(HaveOccurred())
			Expect(ip).To(Equal(ips2[0]))

			// Test fully releasing the second pod CIDR.
			releaseAll(p, ips2)
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: {
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Test fully releasing the first pod CIDR.
			for i, ip := range ips1 {
				Expect(p.release(ip)).To(Succeed())

				ipToOwner, usedIPs, availableIPs, numPodCIDRs, err := p.dump()
				Expect(err).ToNot(HaveOccurred())
				Expect(ipToOwner).ToNot(BeNil())
				Expect(usedIPs).To(Equal(tc.capacity1 - i - 1))
				Expect(availableIPs).ToNot(BeZero())
				Expect(numPodCIDRs).To(Equal(2))

				var expectedStatus2 types.PodCIDRStatus
				if i+1 < p.releaseThreshold {
					expectedStatus2 = types.PodCIDRStatusInUse
				} else {
					expectedStatus2 = types.PodCIDRStatusReleased
				}
				Expect(p.status()).To(Equal(types.PodCIDRMap{
					tc.podCIDR1: {
						Status: types.PodCIDRStatusInUse,
					},
					tc.podCIDR2: {
						Status: expectedStatus2,
					},
				}))
			}
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusInUse,
				},
				tc.podCIDR2: {
					Status: types.PodCIDRStatusReleased,
				},
			}))

			// Release the second pod CIDR.
			p.updatePool([]string{tc.podCIDR1})
			Expect(p.status()).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: {
					Status: types.PodCIDRStatusInUse,
				},
			}))
		})
	}
}

func TestPodCIDRPoolRemoveInUse(t *testing.T) {
	for _, tc := range []struct {
		name           string
		family         Family
		podCIDRs       []string
		allocate       int
		afterPodCIDRs  []string
		expectedStatus types.PodCIDRMap
	}{
		{
			name:   "remove_first_unused",
			family: IPv4,
			podCIDRs: []string{
				"***********/27",
			},
			afterPodCIDRs:  []string{},
			allocate:       0,
			expectedStatus: types.PodCIDRMap{},
		},
		{
			name:   "remove_first_in_use",
			family: IPv4,
			podCIDRs: []string{
				"***********/27",
			},
			afterPodCIDRs: []string{},
			allocate:      1,
			expectedStatus: types.PodCIDRMap{
				"***********/27": {
					Status: types.PodCIDRStatusDepleted,
				},
			},
		},
		{
			name:   "remove_second_unused",
			family: IPv4,
			podCIDRs: []string{
				"***********/27",
				"***********/27",
			},
			allocate: 1,
			afterPodCIDRs: []string{
				"***********/27",
			},
			expectedStatus: types.PodCIDRMap{
				"***********/27": {
					Status: types.PodCIDRStatusInUse,
				},
			},
		},
		{
			name:   "remove_second_in_use",
			family: IPv4,
			podCIDRs: []string{
				"***********/27",
				"***********/27",
			},
			allocate: 31,
			afterPodCIDRs: []string{
				"***********/27",
			},
			expectedStatus: types.PodCIDRMap{
				"***********/27": {
					Status: types.PodCIDRStatusDepleted,
				},
				"***********/27": {
					Status: types.PodCIDRStatusDepleted,
				},
			},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			RegisterTestingT(t)

			p := newPodCIDRPool(0, 0, nil)
			p.updatePool(tc.podCIDRs)
			_ = allocateNextN(p, tc.allocate, nil)
			p.updatePool(tc.afterPodCIDRs)
			Expect(p.status()).To(Equal(tc.expectedStatus))
		})
	}
}

func TestPodCIDRPoolRemoveInUseWithRelease(t *testing.T) {
	RegisterTestingT(t)

	p := newPodCIDRPool(0, 0, nil)

	// Allocate IP from all pod CIDRs.
	p.updatePool([]string{
		"***********/27",
		"***********/27",
		"***********/27",
	})
	_ = allocateNextN(p, 30, mustParseCIDR("***********/27"))
	ip2s := allocateNextN(p, 30, mustParseCIDR("***********/27"))
	_ = allocateNextN(p, 1, mustParseCIDR("***********/27"))
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/27": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/27": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/27": {
			Status: types.PodCIDRStatusInUse,
		},
	}))

	// Remove the second pod CIDR, which is in use.
	p.updatePool([]string{
		"***********/27",
		"***********/27",
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/27": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/27": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/27": {
			Status: types.PodCIDRStatusInUse,
		},
	}))

	// Remove an IP from the second pod CIDR.
	Expect(p.release(ip2s[0])).To(Succeed())

	// Test that new IPs are not allocated from the second pod CIDR.
	ip, err := p.allocateNext()
	Expect(err).ToNot(HaveOccurred())
	Expect(mustParseCIDR("***********/27").Contains(ip)).To(BeTrue())

	// Remove all remaining IPs from the second pod CIDR.
	releaseAll(p, ip2s[1:])
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/27": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/27": {
			Status: types.PodCIDRStatusReleased,
		},
		"***********/27": {
			Status: types.PodCIDRStatusInUse,
		},
	}))
}

func TestPodCIDRPoolDuplicatePodCIDRs(t *testing.T) {
	RegisterTestingT(t)

	// Test that duplicate pod CIDRs are ignored.
	p := newPodCIDRPool(0, 0, nil)
	p.updatePool([]string{
		"***********/27",
		"***********/27",
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/27": {
			Status: types.PodCIDRStatusInUse,
		},
	}))
}

func TestPodCIDRPoolSmallAlloc(t *testing.T) {
	RegisterTestingT(t)

	p := newPodCIDRPool(0, 0, nil)

	// Test that when only a small CIDR is allocated, the CIDR is marked as
	// depleted.
	p.updatePool([]string{
		"***********/30", // Add 2 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/30": {
			Status: types.PodCIDRStatusDepleted,
		},
	}))

	// Test that while the number of available IPs is less than the depleted
	// threshold, all CIDRs are marked as depleted.
	p.updatePool([]string{
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/30": {
			Status: types.PodCIDRStatusDepleted,
		},
		"***********/30": {
			Status: types.PodCIDRStatusDepleted,
		},
	}))

	// Test that when the number of available IPs reaches the depleted
	// threshold, all CIDRs are marked as in use.
	p.updatePool([]string{
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/30": {
			Status: types.PodCIDRStatusInUse,
		},
		"***********/30": {
			Status: types.PodCIDRStatusInUse,
		},
		"***********/30": {
			Status: types.PodCIDRStatusInUse,
		},
		"***********/30": {
			Status: types.PodCIDRStatusInUse,
		},
	}))

	// Test that when a larger CIDR is allocated, the smaller CIDRs are marked as released.
	p.updatePool([]string{
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
		"***********/30", // Add 2 IPs.
		"***********/27", // Add 30 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/30": {
			Status: types.PodCIDRStatusReleased,
		},
		"***********/30": {
			Status: types.PodCIDRStatusReleased,
		},
		"***********/30": {
			Status: types.PodCIDRStatusReleased,
		},
		"***********/30": {
			Status: types.PodCIDRStatusReleased,
		},
		"***********/27": {
			Status: types.PodCIDRStatusInUse,
		},
	}))
}

func TestPodCIDRPoolTooSmallAlloc(t *testing.T) {
	RegisterTestingT(t)

	p := newPodCIDRPool(0, 0, nil)

	// Test that when only a small CIDR is allocated, the CIDR is immediately
	// released.
	p.updatePool([]string{
		"***********/32", // 0 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/32": {
			Status: types.PodCIDRStatusReleased,
		},
	}))
	p.updatePool([]string{
		"***********/31", // 0 IPs.
	})
	Expect(p.status()).To(Equal(types.PodCIDRMap{
		"***********/31": {
			Status: types.PodCIDRStatusReleased,
		},
	}))

}

func TestNewCRDWatcher(t *testing.T) {
	for _, tc := range []struct {
		family    Family
		podCIDR1  string
		capacity1 int
		podCIDR2  string
		capacity2 int
	}{
		{
			family:    IPv4,
			podCIDR1:  "0.0.0.0/27",
			capacity1: 30,
			podCIDR2:  "*******/27",
			capacity2: 30,
		},
		{
			family:    IPv6,
			podCIDR1:  "1::/123",
			capacity1: 30,
			podCIDR2:  "2::/123",
			capacity2: 30,
		},
	} {
		t.Run(string(tc.family), func(t *testing.T) {
			RegisterTestingT(t)

			fakeConfig := &testConfigurationClusterPool{family: tc.family}
			fakeK8sEventRegister := &ownerMock{}
			events := make(chan string, 1)
			fakeK8sNetResourceSetAPI := &fakeK8sNetResourceSetAPI{
				node: &ccev2.NetResourceSet{},
				onDeleteEvent: func() {
					events <- "delete"
				},
				onUpsertEvent: func() {
					events <- "upsert"
				},
			}

			// Test that the watcher updates the NetResourceSet CRD.
			c := newCRDWatcher(fakeConfig, fakeK8sNetResourceSetAPI, fakeK8sEventRegister, fakeK8sNetResourceSetAPI)
			c.localNetResourceUpdated(&ccev2.NetResourceSet{
				Spec: ccev2.NetResourceSpec{
					IPAM: types.IPAMSpec{
						PodCIDRs: []string{
							tc.podCIDR1,
						},
					},
				},
			})
			pool := <-c.waitForPool(tc.family)
			c.restoreFinished()
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode()).NotTo(BeNil())
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Deplete all IPs in the first pod CIDR.
			ip1s := allocateNextN(pool, tc.capacity1, nil)
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusDepleted,
				},
			}))

			// Allocate the second pod CIDR.
			c.localNetResourceUpdated(&ccev2.NetResourceSet{
				Spec: ccev2.NetResourceSpec{
					IPAM: types.IPAMSpec{
						PodCIDRs: []string{
							tc.podCIDR1,
							tc.podCIDR2,
						},
					},
				},
			})
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Allocate all IPs in the second pod CIDR.
			ip2s := allocateNextN(pool, tc.capacity2, nil)
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusDepleted,
				},
			}))

			// Release all IPs in the second pod CIDR.
			releaseAll(pool, ip2s)
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusDepleted,
				},
				tc.podCIDR2: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Release all IPs in the first pod CIDR.
			releaseAll(pool, ip1s)
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusInUse,
				},
				tc.podCIDR2: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusReleased,
				},
			}))

			// Deallocate the second pod CIDR.
			c.localNetResourceUpdated(&ccev2.NetResourceSet{
				Spec: ccev2.NetResourceSpec{
					IPAM: types.IPAMSpec{
						PodCIDRs: []string{
							tc.podCIDR1,
						},
					},
				},
			})
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("upsert"))
			Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
				tc.podCIDR1: types.PodCIDRMapEntry{
					Status: types.PodCIDRStatusInUse,
				},
			}))

			// Delete the node.
			fakeK8sNetResourceSetAPI.deleteNode()
			c.controller.TriggerController(clusterPoolStatusControllerName)
			Expect(<-events).To(Equal("delete"))
			Expect(fakeK8sNetResourceSetAPI.currentNode()).To(BeNil())
		})
	}
}

func TestNewCRDWatcher_restoreFinished(t *testing.T) {
	RegisterTestingT(t)

	fakeConfig := &testConfigurationClusterPool{family: IPv4}
	fakeK8sEventRegister := &ownerMock{}
	events := make(chan string, 1)
	fakeK8sNetResourceSetAPI := &fakeK8sNetResourceSetAPI{
		node: &ccev2.NetResourceSet{},
		onDeleteEvent: func() {
			events <- "delete"
		},
		onUpsertEvent: func() {
			events <- "upsert"
		},
	}

	c := newCRDWatcher(fakeConfig, fakeK8sEventRegister, fakeK8sEventRegister, fakeK8sNetResourceSetAPI)
	c.localNetResourceUpdated(&ccev2.NetResourceSet{
		Spec: ccev2.NetResourceSpec{
			IPAM: types.IPAMSpec{
				PodCIDRs: []string{
					"***********/24",
					"***********/24",
				},
			},
		},
	})

	// Test NetResourceSet CRD is _not_ updated before restoredFinished is called
	c.triggerWithReason("unit test")
	select {
	case e := <-events:
		t.Fatalf("received unexpected event %q", e)
	case <-time.After(10 * time.Millisecond):
	}
	Expect(fakeK8sNetResourceSetAPI.currentNode()).To(Equal(&ccev2.NetResourceSet{}))

	// Test NetResourceSet CRD is updated after restore has finished
	c.restoreFinished()
	Expect(<-events).To(Equal("upsert"))
	Expect(fakeK8sNetResourceSetAPI.currentNode().Status.IPAM.PodCIDRs).To(Equal(types.PodCIDRMap{
		"***********/24": types.PodCIDRMapEntry{
			Status: types.PodCIDRStatusInUse,
		},
		"***********/24": types.PodCIDRMapEntry{
			Status: types.PodCIDRStatusReleased,
		},
	}))
}

// allocateNextN allocates the next n IPs from pool. If cidr is not nil then it
// expects that it will contain all allocated IPs.
func allocateNextN(p *podCIDRPool, n int, cidr *net.IPNet) []net.IP {
	ips := make([]net.IP, 0, n)
	for i := 0; i < n; i++ {
		ip, err := p.allocateNext()
		Expect(err).ToNot(HaveOccurred())
		Expect(ip).ToNot(BeNil())
		if cidr != nil {
			Expect(cidr.Contains(ip)).To(BeTrue())
		}
		ips = append(ips, ip)
	}
	return ips
}

// mustParseCIDR parses a CIDR from s.
func mustParseCIDR(s string) *net.IPNet {
	_, cidr, err := net.ParseCIDR(s)
	Expect(err).ToNot(HaveOccurred())
	return cidr
}

// releaseAll releases ips from the pool. It expects that all releases succeed.
func releaseAll(p *podCIDRPool, ips []net.IP) {
	for _, ip := range ips {
		Expect(p.release(ip)).To(Succeed())
	}
}
