# 自动规划集群模式
## 1. 愿景目标
自动规划集群模式的目的是提升集群的易用性和降低用户的运维成本。长远的愿景是在完全兼容 k8s 一致性认证的基础上，用户仅需维护自己的工作负载，CCE 自动完成 master 组件、节点、网络、存储等基础设施的托管治理。
**自动规划集群把 CCE 的产品能力从“管理基础设施”提升为“管理应用”。**

自动规划集群与普通集群最典型的区别是：
* 自动规划集群：为用户提供基于应用可用性的服务质量协议，即应用 SLA 协议。
* 普通集群（现状）：为用户提供基于master可用性的服务质量协议，即apiserver API SLA 协议。

> * 自动规划集群和托管集群之间的关系是什么？
* 自动规划集群如何定义一个恰当的名字。
* 是不是能把所有的托管集群都启用自动规划集群模式，进一步减少产品的复杂性，减少用户运维成本？

### 1.1 自动规划集群的建设思路
自动规划集群把 CCE 的产品能力从“管理基础设施”提升为“管理应用”。这个改变相对现有系统来说是一个巨大的革命，它需要重新思考 CCE 的产品架构， 并探讨如何与现有的系统相结合。自动规划集群的核心实现思想是：
1. 具备简单易用的产品体验，遵守 k8s 一致性认证。始终保持简单，降低用户的学习成本。
2. 资源管理能力，我们想要用户可以轻松的使用集群，无需再关注计算、网络、存储等基础设施的资源。向用户仅提供容器服务，屏蔽底层基础设施的资源管理。包括 master、节点资源、镜像、网络、存储等。
3. 具备高可用、高可靠的能力。我们希望用户可以轻松的使用集群，无需再关注 master、系统组件、节点的可用性。
4. 具备系统自平衡、快速恢复和故障自愈的能力。数千个集群，每个集群都有几十到几百个节点，出问题是必然的。关键的破局点在故障后的快速恢复，其次是故障自愈。例如用户节点故障，应先替换故障节点，再对故障节点做自愈修复。
5. 稳定且一致的技术设施：贯彻不可变的基础设施特性，支持有限的经过验证的稳定操作系统和内。通过全集群基础设施的一致性和不可变性约定，可以保证用户在集群中创建的资源，在故障后可以随时替换，在变更时可以预期结果。用户在发生故障后，首先考虑的是在一致的基础设施下渐进执行替换，如果替换后问题依然存在，则考虑故障自愈。

### 1.2 哪些不是目标
* 为用户提供更多的机器、master 指标、监控、报警等。用户希望不再关注基础设施的运维，给用户更多时间关注业务。
* 为 CCE 研发人员增加更多的报警的监控。数千个集群，超出 CCE 研发人员的人力治理能力。对所有的问题，都应在设计时考虑容错、高可用、自恢复的能力。所有的故障处理一概使用自动化的方式解决。
* 提供强大且复杂的机器、网络等配置和自定义能力： 复杂的定制能力与自动规划集群的易用性和一致性目标是相悖的。如果支持了用户的定制化配置的能力，自动规划集群的易用性和一致性目标就失去了。在多个用户都有复杂的定制化需求时，CCE 的研发人员将面临无法满足客需的巨大挑战，陷入不可持续发展的问题多-人疲惫-用户体验差的困境。

### 1.3 自动规划集群的短期目标
自动规划集群的产品目标是把 CCE 的产品能力从“管理基础设施”提升为“管理应用”。所以在网络方向，**自动规划集群的目标是向用户屏蔽所有网络资源的管理和维护。** 
即用户在创建集群时，不再需要指定子网、安全组等复杂的配置信息。系统会自动根据容器地址段（可以使用由 CCE 自动生成的地址范围），帮助用户计算出集群最大节点和 Pod 数量。达到让用户简单使用，免网络运维的目的。
自动规划出子网等资源。
在这种模式下，CCE 会根据用户加入的节点不同的地域，自动创建子网和 IP 地址范围，并在 IP 地址不足时，自动扩容创建新的子网。

自动规划集群的目的是提升CCE 的易用性，我们对自动规划集群的预期是：
* 用户可以快速创建集群，不需要在 CCE 上配置并关注网络的细节。完全不需要用户关注任何子网、安全组/ACL、ENI 等网络资源。
* 集群有资源规划能力，最大可以支持 5000 节点，集群最多支持 15000 个 Pod。并能根据用户需求，自动扩容子网。
* 集群中所有的 Pod 和节点之间不会因为 ACL/安全组策略而无法通信。
* 自动规划集群支持与百度智能云上的其他所有网络产品的互联互通，如专线网关、NAT 网关等。自定义集群应拥有相同的扩展能力。

## 2. 用户使用流程
### 2.1 创建集群
1. 创建托管集群时，默认使用自动规划集群网络模式。
2. 默认用户无需指定容器地址段和 svc 地址段
2.1 用户未指定容器地址段时，默认使用 16 位掩码的地址创建一个 VPC 辅助网段。该网段尽量与同地域的 VPC 网段隔离，防止与同地域的 VPC 网段冲突。如果无法自动计算出合适的网段，则需要用户手动分配地址段。
2.2 提供高级设置能力，允许用户手动填入地址段，最小为16位掩码，最大为8位掩码。用户手动填入地址段时，需要保证地址段与同地域的 VPC 网段不冲突。
3. 根据自动生成的地址段，或者用户填入的地址段，给用户提示使用百度云主流机型支持的最大节点数和 Pod 数。 例如使用bcc.g5.c16m64机型，最大节点数和 Pod 数分别为 16 和 256。
3.1 用户可以在集群资源规划计算器中选择不同机型（EBC/BCC/BBC） 和 不同 CPU 内存。
3.2 用户可以在集群资源规划计算器中，选择单机最大容器数，默认 128.
4. 创建集群最后的配置确认页面，要给出用户集群使用的 VPC，**容器网段（VPC 辅助网段）**和 service 网段。以及重要的是要给出集群资源规划信息，记录用户主选的机型和单机 pod 数量，以及需要用户再次确认的集群最大节点和 pod 数。
5. 用户确认后，创建集群。

### 2.2 查看集群
1. 集群详情页需要展示集群的 VPC 信息，容器网段和 service 网段。另外需要标明集群开启了自动规划。
2. 集群详情页需要展示用户集群已有的节点总数，以及每个节点的 pod 数量。以及资源规划信息
2.1 根据用户的上次选择的偏向机型和单机 pod 数量，估算出用户集群剩余最大可创建节点数和 pod 数。
2.2 资源估算时应考虑配额限制，并给出提示，例如用户 BCC 实例配额为 500，会影响用户继续新增节点。

### 2.3 新增节点/组
1. 当用户选择机型后，按照资源规划计算器的算法，推算出节点、节点组最大容量，并给出用户提示。

### 2.4 删除集群
1. 用户操作删除集群操作不变，CCE 需要考虑自动清理集群创建的 VPC-辅助网段。
2. 清空辅助网段下关联的所有 ENI 等资源。

## 3. 技术设计
### 3.1 当前 VPC 网络的限制
#### 3.1.1 弹性网卡和 IP 地址容量的限制
* BCC实例与弹性网卡必须在同一可用区、同一VPC，可以在不同子网，挂载不同的安全组。
* 在一台实例上挂载多个弹性网卡不能提高实例内网带宽性能。
* 每个VPC可支持500个弹性网卡。如有需要，可以在配额中心申请提升配额。
* 单网卡上IP数量最少1个，最多40个。
* 云主机可挂载的弹性网卡数量为：当云主机cpu核数小于等于8时，最大可挂载的弹性网卡数量等于云主机cpu核数；当云主机cpu核数大于8时，最大可挂载8个弹性网卡
####  3.1.2 每个弹性网卡可绑定 IP 地址数量和资源之间的关系
| 内存 |	IP数量| 
| --- | --- | 
| 1G |	2 |
| (1-8]G |	8 |
| (8-32]G |	16 |
| (32-64]G |	30 |
| 大于64G |	40 |

### 3.1.3 子网CIDR与Pod容量
每个子网都必须具有一个 IP 地址范围 CIDR。这是百度云用于为Pod、负载均衡器和节点分配 IP 地址的 IP 地址范围。创建子网后，您无法再缩小或更改子网的CIDR。
* 子网的 CIDR的第一 IP 地址和最后一个 IP 地址由百度智能云预留。
* ENI 的主 IP 也需要消耗子网中的一个 IP 地址，且 pod 无法直接使用该 IP 地址。

下表显示了在特定的子网主要 IP 地址范围大小情况下，您可以在所有使用该子网的集群中创建的最大Pod数。此表假定每个节点的 Pod 数上限为 128，这也是默认的 Pod 密度。

| 子网主要 IP 范围 | 最大 Pod 地址数|	最大节点数 | 最大 Pod 数量 |
| --- | --- |
| /24 次要范围分配方法由用户管理时的最小 Pod IP 范围 |	254 个地址 |1 个节点 |	128 个 Pod|
| /23
仅在次要范围分配方法由用户管理时可用 |	512 个地址 |	2 个节点 |	220 个 Pod |
| /22
仅在次要范围分配方法由用户管理时可用 |	1024 个地址	4 个节点 |	440 个地址 |
| /21
次要范围分配方法由 GKE 管理时的最小 Pod IP 范围 |	2048 个地址 |	8 个节点 |	880 个 Pod |
| /20 |	4096 个地址 |	16 个节点 |	1760 个 Pod |
| /19 |	8192 个地址 |	32 个节点	| 3520 个 Pod |
| /18 |	16384 个地址 |	64 个节点 |	7040 个 Pod |
| /17 |	32768 个地址 |	128 个节点 |	14080 个 Pod |
| /16 |	65536 个地址 |	256 个节点 |	28160 个 Pod |
| /15 |	131072 个地址 |	512 个节点 |	56320 个 Pod |
| /14 |	262144 个地址 |	1024 个节点 |	112640 个 Pod |
| /13	| 524288 个地址	| 2048 个节点 |	225280 个 Pod |
| /12	| 1048576 个地址 |	4096 个节点	 | 450560 个 Pod |
| /11   |
2097152 个地址 |	8192 个节点 |	901120 个 Pod |

### 3.2 自动规划可能遇到的技术问题

* 辅助网段技术：为集群自动规划辅助网段，默认掩码/17，最大掩码/16。辅助网段默认与 VPC 主网段和其他辅助网段不重叠，但和其他网段保持互通。
* 弹性子网技术：为集群自动规划弹性子网，默认掩码/21，最大掩码/17。随节点加入创建，随节点删除释放。
* 安全组/ACL联通保障： 为集群自动规划安全组，并在用户做出非预期修改后，记录异常事件，并自动恢复。
* 故障发现和隔离技术： 自动发现节点网络故障，并实时隔离故障节点。例如 ENI 故障、节点故障等。
* Guaranteed ENI Pool技术： 通过创建节点即挂载所有 ENI 并绑定辅助 IP，保证一个节点只要就绪，永远不会出现 IP 地址不足的问题。

## 最佳实践
规划所需的 IP 地址配额。
根据需要使用非 RFC 1918 空间。
使用自定义子网模式。
规划每个节点的 pod 密度。
避免与其他环境中使用的 IP 地址重叠。
创建负载均衡器子网。
为集群自动扩缩器预留足够的 IP 地址空间。
在集群之间共享 IP 地址。
共享内部 LoadBalancer Service 的 IP 地址。