/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package api

import (
	"github.com/go-openapi/runtime/middleware"

	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/api/v1/operator/server/restapi/operator"
)

type getHealthz struct {
	*Server
}

// NewGetHealthzHandler handles health requests.
func NewGetHealthzHandler(s *Server) operator.GetHealthzHandler {
	return &getHealthz{Server: s}
}

// Handle handles GET requests for /healthz .
func (h *getHealthz) Handle(params operator.GetHealthzParams) middleware.Responder {
	select {
	// only start serving the real health check once all systems all up and running
	case <-h.Server.allSystemsGo:
		if err := h.Server.checkStatus(); err != nil {
			log.WithError(err).Warn("Health check status")

			return operator.NewGetHealthzInternalServerError().WithPayload(err.Error())
		}
	default:
	}

	return operator.NewGetHealthzOK().WithPayload("ok")
}
