package main

import (
	"fmt"
	"net"

	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging"
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging/logfields"
	types100 "github.com/containernetworking/cni/pkg/types/100"
	"github.com/containernetworking/plugins/pkg/ns"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/mock"
)

type MockNetworkChecker struct {
	mock.Mock
}

func (m *MockNetworkChecker) GetDefaultGateway() (net.IP, error) {
	args := m.Called()
	ip := args.Get(0)
	if ip == nil {
		return nil, args.Error(1)
	}
	return ip.(net.IP), args.Error(1)
}

func (m *MockNetworkChecker) SendIcmpProbeInNetNS(netns ns.NetNS, srcIP, targetIP net.IP, mtu int) (bool, error) {
	args := m.Called(netns, srcIP, targetIP, mtu)
	return args.Bool(0), args.Error(1)
}

var _ NetworkChecker = &MockNetworkChecker{}

var _ = Describe("VerifyNetworkConnectivity", func() {
	var mockTargetNS *MockNetNS
	var mockUtils *MockNetworkUtils

	logger = logging.DefaultLogger.WithFields(logrus.Fields{
		"cmdArgs": logfields.Json("test"),
		"plugin":  "cptp",
		"mod":     "ADD",
	})

	BeforeEach(func() {
		mockTargetNS = &MockNetNS{path: "/var/run/netns/test"}
		mockUtils = &MockNetworkUtils{}
	})

	AfterEach(func() {
		// 无需清理模拟对象
	})

	It("should verify network connectivity for IPv4 addresses", func() {
		result := &types100.Result{
			IPs: []*types100.IPConfig{
				{
					Address: net.IPNet{
						IP:   net.ParseIP("***********0"),
						Mask: net.CIDRMask(24, 32),
					},
					Gateway: net.ParseIP("***********"),
				},
			},
		}

		mockUtils.On("SendIcmpProbeInNetNS", mockTargetNS, net.ParseIP("***********0"), net.ParseIP("***********"), 1500).Return(true, nil)

		// 模拟 targetNS.Do 调用成功
		mockTargetNS.On("Do", mock.AnythingOfType("func(ns.NetNS) error")).Return(nil).Run(func(args mock.Arguments) {
			fn := args.Get(0).(func(ns.NetNS) error)
			fn(mockTargetNS) // 执行传入的函数
		})

		err := mockTargetNS.Do(func(ns.NetNS) error {
			defer GinkgoRecover()

			err := VerifyNetworkConnectivity(result, mockTargetNS, 1500, mockUtils)
			Expect(err).NotTo(HaveOccurred())
			return nil
		})
		Expect(err).NotTo(HaveOccurred())
	})

	It("should skip non-IPv4 addresses", func() {
		result := &types100.Result{
			IPs: []*types100.IPConfig{
				{
					Address: net.IPNet{
						IP:   net.ParseIP("2001:db8::1"),
						Mask: net.CIDRMask(64, 128),
					},
					Gateway: net.ParseIP("2001:db8::1"),
				},
			},
		}

		// 模拟 targetNS.Do 调用成功
		mockTargetNS.On("Do", mock.AnythingOfType("func(ns.NetNS) error")).Return(nil).Run(func(args mock.Arguments) {
			fn := args.Get(0).(func(ns.NetNS) error)
			fn(mockTargetNS) // 执行传入的函数
		})

		err := mockTargetNS.Do(func(ns.NetNS) error {
			defer GinkgoRecover()

			err := VerifyNetworkConnectivity(result, mockTargetNS, 1500, mockUtils)
			Expect(err).NotTo(HaveOccurred())
			return nil
		})
		Expect(err).NotTo(HaveOccurred())
	})

	It("should return error if failed to get default gateway for vpc route mode", func() {
		result := &types100.Result{
			IPs: []*types100.IPConfig{
				{
					Address: net.IPNet{
						IP:   net.ParseIP("***********0"),
						Mask: net.CIDRMask(32, 32),
					},
					Gateway: nil,
				},
			},
		}

		mockUtils.On("GetDefaultGateway").Return(nil, fmt.Errorf("failed to get default gateway"))

		// 模拟 targetNS.Do 调用成功
		mockTargetNS.On("Do", mock.AnythingOfType("func(ns.NetNS) error")).Return(nil).Run(func(args mock.Arguments) {
			fn := args.Get(0).(func(ns.NetNS) error)
			fn(mockTargetNS) // 执行传入的函数
		})

		err := mockTargetNS.Do(func(ns.NetNS) error {
			defer GinkgoRecover()

			err := VerifyNetworkConnectivity(result, mockTargetNS, 1500, mockUtils)
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("failed to get default gateway for vpc route mode"))
			return nil
		})
		Expect(err).NotTo(HaveOccurred())
	})

	It("should return error if failed to verify network connectivity", func() {
		result := &types100.Result{
			IPs: []*types100.IPConfig{
				{
					Address: net.IPNet{
						IP:   net.ParseIP("***********0"),
						Mask: net.CIDRMask(24, 32),
					},
					Gateway: net.ParseIP("***********"),
				},
			},
		}

		mockUtils.On("SendIcmpProbeInNetNS", mockTargetNS, net.ParseIP("***********0"), net.ParseIP("***********"), 1500).Return(false, fmt.Errorf("failed to send ICMP probe"))

		// 模拟 targetNS.Do 调用成功
		mockTargetNS.On("Do", mock.AnythingOfType("func(ns.NetNS) error")).Return(nil).Run(func(args mock.Arguments) {
			fn := args.Get(0).(func(ns.NetNS) error)
			fn(mockTargetNS) // 执行传入的函数
		})

		err := mockTargetNS.Do(func(ns.NetNS) error {
			defer GinkgoRecover()

			err := VerifyNetworkConnectivity(result, mockTargetNS, 1500, mockUtils)
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("failed to verify network connectivity"))
			return nil
		})
		Expect(err).NotTo(HaveOccurred())
	})

	It("should return error if failed to ping gateway", func() {
		result := &types100.Result{
			IPs: []*types100.IPConfig{
				{
					Address: net.IPNet{
						IP:   net.ParseIP("***********0"),
						Mask: net.CIDRMask(24, 32),
					},
					Gateway: net.ParseIP("***********"),
				},
			},
		}

		mockUtils.On("SendIcmpProbeInNetNS", mockTargetNS, net.ParseIP("***********0"), net.ParseIP("***********"), 1500).Return(false, nil)

		// 模拟 targetNS.Do 调用成功
		mockTargetNS.On("Do", mock.AnythingOfType("func(ns.NetNS) error")).Return(nil).Run(func(args mock.Arguments) {
			fn := args.Get(0).(func(ns.NetNS) error)
			fn(mockTargetNS) // 执行传入的函数
		})

		err := mockTargetNS.Do(func(ns.NetNS) error {
			defer GinkgoRecover()

			err := VerifyNetworkConnectivity(result, mockTargetNS, 1500, mockUtils)
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("failed to ping gateway"))
			return nil
		})
		Expect(err).NotTo(HaveOccurred())
	})
})
