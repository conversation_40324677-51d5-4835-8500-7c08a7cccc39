// Copyright 2015 CNI authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package main

import (
	"fmt"
	"net"

	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging/logfields"
	current "github.com/containernetworking/cni/pkg/types/100"
	"github.com/containernetworking/plugins/pkg/ip"
	"github.com/containernetworking/plugins/pkg/utils/sysctl"

	"github.com/vishvananda/netlink"
)

const (
	// Note: use slash as separator so we can have dots in interface name (VLANs)
	DisableIPv6SysctlTemplate = "net/ipv6/conf/%s/disable_ipv6"
)

// ConfigureIface takes the result of IPAM plugin and
// applies to the ifName interface
// this method is diffrent from the original cni ipam in ipam_linux.go
// it will not set the ip address to the interface and will set the default gateway to the interface onlink
func CPTPConfigureIface(ifName string, res *current.Result) error {
	if len(res.Interfaces) == 0 {
		return fmt.Errorf("no interfaces to configure")
	}

	link, err := netlink.LinkByName(ifName)
	if err != nil {
		return fmt.Errorf("failed to lookup %q: %v", ifName, err)
	}

	var v4gw, v6gw net.IP
	var has_enabled_ipv6 bool = false
	for _, ipc := range res.IPs {
		if ipc.Interface == nil {
			continue
		}
		intIdx := *ipc.Interface
		if intIdx < 0 || intIdx >= len(res.Interfaces) || res.Interfaces[intIdx].Name != ifName {
			// IP address is for a different interface
			return fmt.Errorf("failed to add IP addr %v to %q: invalid interface index", ipc, ifName)
		}

		// Make sure sysctl "disable_ipv6" is 0 if we are about to add
		// an IPv6 address to the interface
		if !has_enabled_ipv6 && ipc.Address.IP.To4() == nil {
			// Enabled IPv6 for loopback "lo" and the interface
			// being configured
			for _, iface := range [2]string{"lo", ifName} {
				ipv6SysctlValueName := fmt.Sprintf(DisableIPv6SysctlTemplate, iface)

				// Read current sysctl value
				value, err := sysctl.Sysctl(ipv6SysctlValueName)
				if err != nil {
					logger.WithError(err).Errorf("ipam_linux: failed to read sysctl %s", ipv6SysctlValueName)
					continue
				}
				if value == "0" {
					continue
				}

				// Write sysctl to enable IPv6
				_, err = sysctl.Sysctl(ipv6SysctlValueName, "0")
				if err != nil {
					return fmt.Errorf("failed to enable IPv6 for interface %q (%s=%s): %v", iface, ipv6SysctlValueName, value, err)
				}
			}
			has_enabled_ipv6 = true
		}

		var netMask = net.CIDRMask(32, 32)
		if ipc.Address.IP.To4() == nil {
			netMask = net.CIDRMask(128, 128)
		}
		addr := &netlink.Addr{
			IPNet: &net.IPNet{
				IP:   ipc.Address.IP,
				Mask: netMask,
			},
			Label: "",
		}
		if err = netlink.AddrAdd(link, addr); err != nil {
			logger.WithError(err).WithField("add", logfields.Repr(addr)).Errorf("failed to add IP addr %v to %q: %v", ipc, ifName, err)
			return fmt.Errorf("failed to add IP addr %v to %q: %v", ipc, ifName, err)
		}

		gwIsV4 := ipc.Gateway.To4() != nil

		if gwIsV4 && v4gw == nil {
			v4gw = ipc.Gateway
		} else if !gwIsV4 && v6gw == nil {
			v6gw = ipc.Gateway
		}
	}

	if err := netlink.LinkSetUp(link); err != nil {
		return fmt.Errorf("failed to set %q UP: %v", ifName, err)
	}

	if v6gw != nil {
		ip.SettleAddresses(ifName, 10)
	}

	// add default route
	for _, ipc := range res.IPs {
		addrBits := 32
		if ipc.Address.IP.To4() == nil {
			addrBits = 128
		}

		for _, r := range []netlink.Route{
			{
				LinkIndex: link.Attrs().Index,
				Dst: &net.IPNet{
					IP:   ipc.Gateway,
					Mask: net.CIDRMask(addrBits, addrBits),
				},
				Scope: netlink.SCOPE_LINK,
				Src:   ipc.Address.IP,
			},
		} {
			if err := netlink.RouteAdd(&r); err != nil {
				logger.WithError(err).WithField("route", logfields.Repr(r)).Errorf("failed to add route")
				return fmt.Errorf("failed to add route %v: %v", r, err)
			}
		}
	}

	// add customer routes
	for _, r := range res.Routes {
		routeIsV4 := r.Dst.IP.To4() != nil
		gw := r.GW
		if gw == nil {
			if routeIsV4 && v4gw != nil {
				gw = v4gw
			} else if !routeIsV4 && v6gw != nil {
				gw = v6gw
			}
		}
		route := netlink.Route{
			Dst:       &r.Dst,
			LinkIndex: link.Attrs().Index,
			Gw:        gw,
		}

		if err = netlink.RouteAddEcmp(&route); err != nil {
			logger.WithError(err).WithField("route", logfields.Repr(r)).Errorf("failed to add route")
			return fmt.Errorf("failed to add route '%v via %v dev %v': %v", r.Dst, gw, ifName, err)
		}
	}

	return nil
}
