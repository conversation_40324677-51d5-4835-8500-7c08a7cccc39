# Copyright Authors of CCE
# SPDX-License-Identifier: Apache-2.0

ifeq ($(ROOT_DIR),)
	ROOT_DIR ?= $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))
	RELATIVE_DIR ?= $(shell echo $(realpath .) | sed "s;$(ROOT_DIR)[/]*;;")
endif
ifeq ($(V),0)
	QUIET=@
	ECHO_CC=echo "  CC     $(RELATIVE_DIR)/$@"
	ECHO_CHECK=echo "  CHECK  $(RELATIVE_DIR)"
	ECHO_CLEAN=echo "  CLEAN  $(RELATIVE_DIR)"
	ECHO_DOCKER=echo "  DOCKER $(RELATIVE_DIR) $@"
	ECHO_GEN=echo "  GEN    $(RELATIVE_DIR)/"
	ECHO_GINKGO=echo "  GINKGO $(RELATIVE_DIR)"
	ECHO_GO=echo "  GO     $(RELATIVE_DIR)/$@"
	ECHO_TEST=echo "  TEST "
	SUBMAKEOPTS="-s"
else
	# The whitespace at below EOLs is required for verbose case!
	ECHO_CC=:
	ECHO_CHECK=:
	ECHO_CLEAN=:
	ECHO_DOCKER=:
	ECHO_GEN=:
	ECHO_GINKGO=:
	ECHO_GO=:
	ECHO_TEST=:
	SUBMAKEOPTS=
endif
