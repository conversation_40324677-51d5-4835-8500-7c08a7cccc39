apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-example
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
        # 声明Pod使用独占ENI
        # cce.baidubce.com/eni-use-mode: Primary
    spec:
      restartPolicy: Always
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #           - operator: In
      #             key: "kubernetes.io/hostname"
      #             values:
      #               - ********
      containers:
        - name: nginx
          image: registry.baidubce.com/cce-plugin-dev/ubuntu-dev-22-04:1.1
          imagePullPolicy: IfNotPresent
          command:
          - sh
          - -c
          - while true; do sleep 1;done;