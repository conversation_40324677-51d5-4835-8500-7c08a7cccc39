apiVersion: apps/v1
kind: Deployment
metadata:
  name: qos
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
        feature: qos
        # 声明Pod使用独占ENI
        # cce.baidubce.com/eni-use-mode: Primary
      annotations:
        kubernetes.io/ingress-bandwidth: 10M
        kubernetes.io/egress-bandwidth: 10M
        cce.baidubce.com/egress-priority: Guaranteed
    spec:
      restartPolicy: Always
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #           - operator: In
      #             key: "kubernetes.io/hostname"
      #             values:
      #               - ********
      containers:
        - name: nginx
          image: registry.baidubce.com/cce-plugin-dev/ubuntu-dev-22-04:1.1
          imagePullPolicy: IfNotPresent
          command:
          - sh
          - -c
          - while true; do sleep 1;done;