# Copyright Authors of CCE
# SPDX-License-Identifier: Apache-2.0

SHELL := /bin/bash
.SHELLFLAGS := -eu -o pipefail -c

# define a function replacing spaces with commas in a list
empty :=
space := $(empty) $(empty)
comma := ,
join-with-comma = $(subst $(space),$(comma),$(strip $1))

ROOT_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))
RELATIVE_DIR := $(shell echo $(realpath .) | sed "s;$(ROOT_DIR)[/]*;;")
include $(ROOT_DIR)/Makefile.quiet

PREFIX?=/usr
BINDIR?=$(PREFIX)/bin
CNIBINDIR?=/opt/cni/bin
CNICONFDIR?=/etc/cni/net.d
LIBDIR?=$(PREFIX)/lib
LOCALSTATEDIR?=/var
RUNDIR?=/var/run
CONFDIR?=/etc

GOOS = linux
NATIVE_ARCH = $(shell GOARCH= $(GO) env GOARCH)
export GOARCH ?= $(NATIVE_ARCH)
TARGET_ARCH ?= $(GOARCH)

INSTALL = install

CONTAINER_ENGINE?=docker
DOCKER_FLAGS?=
DOCKER_BUILD_FLAGS?=

# profile of image registry, dev or pro
PROFILE ?= dev

# use gsed if avaiable, otherwise use sed.
# gsed is needed for MacOS to make in-place replacement work correctly.
SED ?= $(if $(shell command -v gsed), gsed, sed)

# Set DOCKER_DEV_ACCOUNT with "cce" by default
ifeq ($(DOCKER_DEV_ACCOUNT),)
    DOCKER_DEV_ACCOUNT=cce
endif

# Set DOCKER_IMAGE_TAG with "latest" by default
ifeq ($(DOCKER_IMAGE_TAG),)
    DOCKER_IMAGE_TAG=latest
endif

PUSH_IMAGE_FLAGS = --load --push

GO ?= go

# go build/test/clean flags
# these are declared here so they are treated explicitly
# as non-immediate variables
GO_BUILD_FLAGS =
GO_TEST_FLAGS =
GO_CLEAN_FLAGS =
GO_BUILD_LDFLAGS =
# go build/test -tags values
GO_TAGS_FLAGS = osusergo

# This is declared here as it is needed to change the covermode depending on if
# RACE is specified.
GOTEST_COVER_OPTS =

# By default, just print go test output immediately to the terminal. If tparse
# is installed, use it to format the output. This is either silent silent (V=0)
# or gives some signal about ongoing progress by teeing to the terminal (V=1).
GOTEST_FORMATTER ?= cat
ifneq ($(shell which tparse),)
	GOTEST_COVER_OPTS += -json
	GOTEST_FORMATTER = tparse
ifneq ($(V),0)
	GOTEST_FORMATTER += -follow
endif
endif

GOLANGCILINT_WANT_VERSION = 1.46.2
GOLANGCILINT_IMAGE_SHA = sha256:e84b639c061c8888be91939c78dae9b1525359954e405ab0d9868a46861bd21b
GOLANGCILINT_VERSION = $(shell golangci-lint version 2>/dev/null)

VERSION = $(shell cat $(dir $(lastword $(MAKEFILE_LIST)))/VERSION)
VERSION_MAJOR = $(shell cat $(dir $(lastword $(MAKEFILE_LIST)))/VERSION | cut -d. -f1)
# Use git only if in a Git repo
ifneq ($(wildcard $(dir $(lastword $(MAKEFILE_LIST)))/../.git),)
    GIT_VERSION = $(shell git show -s --format='format:%h %aI')
else
    GIT_VERSION = $(shell cat $(ROOT_DIR)/GIT_VERSION)
endif
FULL_BUILD_VERSION = $(VERSION) $(GIT_VERSION)
GO_BUILD_LDFLAGS += -X "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/version.cceVersion=$(FULL_BUILD_VERSION)"

ifeq ($(NOSTRIP),)
    # Note: these options will not remove annotations needed for stack
    # traces, so panic backtraces will still be readable.
    #
    # -w: Omit the DWARF symbol table.
    # -s: Omit the symbol table and debug information.
    GO_BUILD_LDFLAGS += -s -w
endif

ifneq ($(wildcard $(dir $(lastword $(MAKEFILE_LIST)))/images/cce/Dockerfile),)
    CILIUM_ENVOY_REF=$(shell sed -E -e 's/^FROM (--[^ ]* )*([^ ]*) as cce-envoy/\2/p;d' < $(ROOT_DIR)/images/cce/Dockerfile)
    CILIUM_ENVOY_SHA=$(shell echo $(CILIUM_ENVOY_REF) | sed -E -e 's/[^/]*\/[^:]*:([^:@]*).*/\1/p;d')
    GO_BUILD_LDFLAGS += -X "github.com/cce/cce/pkg/envoy.RequiredEnvoyVersionSHA=$(CILIUM_ENVOY_SHA)"
endif
# Set -mod=vendor if running >= go 1.13 or if GO111MODULE is set.
# A go build is being executed with go modules if:
# * The go command is invoked with GO111MODULE=on environment variable set.
# * The go command is invoked in a directory outside of the $GOPATH/src tree
#   and the environment variable GO111MODULE unset (or explicitly set to 'auto').
ifeq ($(GO111MODULE),on)
else
    GO_MAJOR_VERSION_GE_1 := $(shell expr `$(GO) version | grep -E 'go[0-9]+' -o | sed 's/go//g'` \>= 1)
    ifeq ($(GO_MAJOR_VERSION_GE_1),1)
        GO_MINOR_VERSION_GE_13 := $(shell expr `$(GO) version | grep -E 'go[^ ]+' -o | sed 's/go1.//g'` \>= 13)
        ifeq ($(GO_MINOR_VERSION_GE_13),1)
            #GO_BUILD_FLAGS += -mod=vendor
            #GO_TEST_FLAGS += -mod=vendor
            #GO_CLEAN_FLAGS += -mod=vendor
        endif
    endif
endif

GO_BUILD = CGO_ENABLED=0 GOOS=$(GOOS) GOARCH=$(TARGET_ARCH) $(GO) build

GO_BUILD_FLAGS += -ldflags '$(GO_BUILD_LDFLAGS) $(EXTRA_GO_BUILD_LDFLAGS)' -tags=$(call join-with-comma,$(GO_TAGS_FLAGS)) $(EXTRA_GO_BUILD_FLAGS)
GO_TEST_FLAGS += -tags=$(call join-with-comma,$(GO_TAGS_FLAGS))

ifeq ($(NOOPT),1)
    GO_BUILD_FLAGS += -gcflags="all=-N -l"
endif

GO_BUILD += $(GO_BUILD_FLAGS)
GO_BUILD_WITH_CGO += $(GO_BUILD_FLAGS)

GO_TEST = $(GO) test $(GO_TEST_FLAGS)
GO_CLEAN = $(GO) clean $(GO_CLEAN_FLAGS)
# TODO: remove `GO111MODULE=off` once Go 1.13 is deprecated by Go maintainers
GO_VET = GO111MODULE=off $(GO) vet
GO_LIST = GO111MODULE=off $(GO) list

ifeq ($(BASE_IMAGE),)
    BASE_IMAGE=scratch
endif

HELM_DOCS_VERSION ?= "5ddabba"
HELM_DOCS_SHA ?= "c4a91daac6bf9c181fac79e09712fcf1f9102fcab1bbacbc733883965f5fd244"
HELM_DOCS_IMAGE ?= "docker.io/bmcustodio/helm-docs:$(HELM_DOCS_VERSION)@sha256:$(HELM_DOCS_SHA)"

define print_help_line
  @printf "  \033[36m%-29s\033[0m %s.\n" $(1) $(2)
endef

define print_help_from_makefile
  @awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z0-9][a-zA-Z0-9 _-]*:.*?##/ { split($$1, targets, " "); for (i in targets) { printf "  \033[36m%-28s\033[0m %s\n", targets[i], $$2 } } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
endef
