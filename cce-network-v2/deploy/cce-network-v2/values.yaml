# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
network:
  # 镜像后缀，如 -arm64
  imageSuffix: ""
  operator:
    replicaCount: 2
    image:
      # 注意 repository + name 才是完整的镜像名
      repository: registry.baidubce.com/cce-plugin-pro
      # 使用vpc-eni作为operator镜像
      name: cce-network-operator-vpc-eni
      pullPolicy: Always
      # Overrides the image tag whose default is the chart appVersion.
      tag: ""
    resources: { }
    disableHttp2: false
    rdma:
      enable: true
    webhook:
      mutating: cce-network-v2-mutating-webhook
      validating: ""
      enable: true
      resources: { }
      cert: |
        -----BEGIN CERTIFICATE-----
        MIIDqjCCApKgAwIBAgIUbpu2Cda01w82lhZJkO8gFbAF3iswDQYJKoZIhvcNAQEL
        BQAwKTEXMBUGA1UEAwwOY2NlLW5ldHdvcmstdjIxDjAMBgNVBAoMBUJhaWR1MB4X
        DTI1MDQwOTEzMDExOFoXDTQ1MDQwNDEzMDExOFowKTEXMBUGA1UEAwwOY2NlLW5l
        dHdvcmstdjIxDjAMBgNVBAoMBUJhaWR1MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
        MIIBCgKCAQEAmO8gRXmeBm+KwL1c3/Fu9lmUJSYfOUpydk1Y40ZXWxZTr0ymGdDU
        E9ajBnHyc3Vn0uosj9a8TiMJ+Z2czDbO8uHO9WjRMFYsn2w5RtjKLX8p+nfl2GN0
        aqzO1Gwfl3KV2th6ESCuJQCna8r5a71az5j8phB42B3RDkTDpTrPJPP9WEooqp/Y
        C5sqf5seTra7BZz6obziuyjnT9hv2wGrT17PYv+g2iZ75e6F0UavvRrN2HtwirTm
        qPijyyCPh2VR9nYkxs4xYkrCDVMQbD8GNr9aMKktMJdshCgDteH5NEABBCHiKDSN
        JPA9Lzcy/Lrl2TGd+9EkUPwpLUZnwZRfBwIDAQABo4HJMIHGMAkGA1UdEwQCMAAw
        CwYDVR0PBAQDAgXgMBMGA1UdJQQMMAoGCCsGAQUFBwMBMFcGA1UdEQRQME6CDmNj
        ZS1uZXR3b3JrLXYyghtjY2UtbmV0d29yay12Mi5jY2UtOXl6ZDdiaHaCH2NjZS1u
        ZXR3b3JrLXYyLmNjZS05eXpkN2Jodi5zdmMwHQYDVR0OBBYEFHFq3YFad2pqRX8X
        0Z1t8nrLqQkJMB8GA1UdIwQYMBaAFE9jAMTeQgWs3dt72IP9c06U0xOpMA0GCSqG
        SIb3DQEBCwUAA4IBAQCBCMzryYBkfwFN19HZb03zW+WqUYufzMoWjAmgYrG/Ea6+
        FohwHB1+M4oYAfZU9v1pYeVNDkqfMI6+t7umEEndRocC9ipR02bVWA5fUY07RuQx
        5Sy2LXTQKNertw64NtpEf9g6q8/jmA35QIcf/qwN7t7afGGk5GQljMSh1iaxGTSG
        3iduTZJqeGEfDmM2A2QyQCnTRVm5xEvWjTBq1XzlStXxjhiplDOicAi2JS09LYFQ
        0IT3oFGhAhoOvl/S1x+wSFtaHpUvEilxCtUNkwFwv7De0ZKoQfaT148+2hGUDZpw
        jlSSF3zAYCsVp3f/JJ+3B3LaWTfRA27O6lfC8FEl
        -----END CERTIFICATE-----
      key: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      caBundle:
        LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURNekNDQWh1Z0F3SUJBZ0lVUFR6MUxzZGtTK3djYnk5MTZJMVBZNGNNSTRNd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0tURVhNQlVHQTFVRUF3d09ZMk5sTFc1bGRIZHZjbXN0ZGpJeERqQU1CZ05WQkFvTUJVSmhhV1IxTUI0WApEVEkxTURRd09URXpNREV4T0ZvWERUUTFNRFF3TkRFek1ERXhPRm93S1RFWE1CVUdBMVVFQXd3T1kyTmxMVzVsCmRIZHZjbXN0ZGpJeERqQU1CZ05WQkFvTUJVSmhhV1IxTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEEKTUlJQkNnS0NBUUVBdER4Z3ZIVnR3NjRZd1NKNFNlR2pDeHdyRWUxR3dNSm12Mko0UVh4RUV3d01JV2d5OUd1SApta3gzLytmems1UFE2MGU4aXhad2FGSGQ1QzFUeDFEcnVkaUhnb1pIRWNJaXRYdDFhcHlvSGhEMXgxWDQzUDF3CjNPeW56amM0VEpNd1FuamVUaGlyeTBXUlpObGZRU0VycWJoeWxFWFYxVFNBTk5JaC9scUZwa08rNUJ5SElOMU0KZG5vMVB5aEkwMmNPUkdaNUxMeHZvcGhKM1cxdG9SS1JIcjNzVVNWdmJYRTFFbjhXVG92VTBNUXEwbGp4RzBLeApYYVNsZGl6OWkzc2dZNGM5amgrSFowc1UwSUMwa0kvSFBTS0RJbndOWXh3eitJbFkvOGRJWWk5N3RmdHBYVEIxCmozVUJCM1I1dy8wZmVNcTk4Sm5ISXZMaWFORHNMQllHTFFJREFRQUJvMU13VVRBZEJnTlZIUTRFRmdRVVQyTUEKeE41Q0JhemQyM3ZZZy8xelRwVFRFNmt3SHdZRFZSMGpCQmd3Rm9BVVQyTUF4TjVDQmF6ZDIzdllnLzF6VHBUVApFNmt3RHdZRFZSMFRBUUgvQkFVd0F3RUIvekFOQmdrcWhraUc5dzBCQVFzRkFBT0NBUUVBRjk0c0xtOHR3cm9rCitaVW1EeVBRK0lnaGpDQmg4WXRYaVNPcE9FK3dlaHNaazRPbWh1OExwSFZsTS9SNnBQY1g3Q1VwQkI2L29PckQKSlVQRDVkWWdrYmFlaWZDa3BuOWZSczB0MVJZWVVkTkNtOUNZeVRwWG5yTjBwbC9XWm9XU3FMMlV3Z0wzb1djcwpSeUdTWWdtcE5hSHFYZ3NURjh5aGF6NW9pWCtZcXlIUktoNTNibFdONk9wMGdxTEM0V2lWS1MwckFpdURSQUNCCjFTRXNESTJic3F4OGJDUnlBL09MMVg4QkJVcUJFWmI1Rlk5L2pMUjJjY3pHVWROZS9YNVR1T2taYXFlRzBVNGcKQi9QdDFwY2ptVFVQU3RRdmJ6SmNpczJEdGsyVVpoRmxsZEVkeGxYS1VPMWQ1bCtTNkNqMWZjRXc4NWwzM2h4UAorZWVOMFNtek1nPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
    affinity:
      nodeAffinity:
        preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 1
          preference:
            matchExpressions:
            - key: cluster-role
              operator: In
              values: ["master"]
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
          - matchExpressions:
            - key: type
              operator: NotIn
              values:
                - virtual-kubelet
  agent:
    name: agent
    image:
      repository: registry.baidubce.com/cce-plugin-pro/cce-network-agent
      pullPolicy: Always
      # Overrides the image tag whose default is the chart appVersion.
      tag: ""
    resources: { }
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
              - key: type
                operator: NotIn
                values:
                  - virtual-kubelet
    updateStrategy:
      type: RollingUpdate
      rollingUpdate:
        maxUnavailable: 5%
tolerations:
  - operator: "Exists"

# 此处的部署模式分为三种：
# standalone: 独立部署方式，所有组件都部署在用户集群
# pro-meta: 托管部署模式下部署在pro-meta集群的部分组件
# incluster: 托管部署模式下部署在用户集群的部分组件
DeploymentMod: "standalone"

apiserverServiceEnv:
  enable: false
  kubernetesServiceHost: ""
  kubernetesServicePort: ""

imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "19965"
  prometheus.io/path: "/metrics"

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

nodeSelector: { }

affinity: { }

extplugins: {}
  # cilium-cni:
  #   type: cilium-cni
  # sbr-eip:
  #   type: sbr-eip  

# cce守护进程配置
ccedConfig:
  # operator BCE VPC 配置
  cce-cluster-id: ""
  bce-cloud-vpc-id: "" 
  bce-cloud-access-key: ""
  bce-cloud-secure-key: ""
  # 云的可用区，用于选择Cloud API的地址
  bce-cloud-country: cn
  bce-cloud-region: ""
  bce-cloud-host: ""

  # 开启debug模式(会多打日志)
  debug: false
  # 在非k8s环境运行使用
  # k8s-kubeconfig-path: /run/kubeconfig
  k8s-client-burst: 300
  k8s-client-qps: 200
  k8s-api-discovery: false
  leader-election-lease-duration: 60s
  leader-election-renew-deadline: 30s
  # 启用自动创建cce node资源
  skip-manager-node-labels:
    type: virtual-kubelet
  auto-create-network-resource-set-resource: true
  enable-monitor: false
  nodes-gc-interval: 30s
  skip-crd-creation: true
  mtu: 1500
  # 启动ipv4
  enable-ipv4: true
  enable-ipv6: false
  enable-rdma: false
  # api 限流配置
  default-api-burst: 100
  default-api-qps: 50
  default-api-timeout: 30s
  api-rate-limit:
    # bce api 限流
    bcecloud/apis/v1/BatchAddPrivateIP: "rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true"
    bcecloud/apis/v1/BatchDeletePrivateIP: "rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true"
    bcecloud/apis/v1/AttachENI: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true"
    bcecloud/apis/v1/CreateENI: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true"
    bcecloud/apis/v1/DescribeSubnet: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5"
    bcecloud/apis/v1/StatENI: "rate-limit:10/1s,rate-burst:15,max-wait-duration:30s,parallel-requests:10"

  # 调试
  pprof: false
  pprof-port: 14386
  health-port: 19879
  gops-port: 19891
  prometheus-serve-addr: ":19962"
  # 开启operator metrics
  enable-metrics: true
  operator-prometheus-serve-addr: ":19965"
  operator-api-serve-addr: ":19234"
  log-driver: syslog
  log-opt: |
    {"syslog.level":"info","syslog.facility":"local5"}
  # cni 配置，从指定文件读取，写入到另一个路径
  write-cni-conf-when-ready: ""

  # ipam 模式. privatecloudbase: 私有云底座;vpc-eni: BCE VPC ENI
  ipam: vpc-eni
  endpoint-gc-interval: 30s

  # vpc 资源同步周期
  resource-resync-interval: 10s
  # eni 资源同步周期
  resource-eni-resync-interval: 5s
  # 资源重新同步的并发协程数
  resource-resync-workers: 64
  nrs-resource-resync-workers: 1000
  rdma-resource-resync-workers: 1000
  eni-resource-resync-workers: 1000
  subnet-resource-resync-workers: 10

  # agent vpc-eni 配置
  # 禁用ENI CRD
  disable-eni-crd: false
  # ENI使用模式：Secondary：辅助IP；Primary：主IP（独占ENI）
  eni-use-mode: Secondary
  eni-install-source-based-routing: true
  eni-subnet-ids: ""
  eni-security-group-ids: ""
  eni-enterprise-security-group-ids: ""

  eni-pre-allocate-num: 1
  eni-route-table-offset: 127
  # 启用burstable ENI池，默认使用保持最少 1 个 ENI 空闲
  burstable-mehrfach-eni: 0
  # ippool 优化配置
  ippool-min-allocate-ips: 10
  ippool-pre-allocate: 2
  ippool-max-above-watermark: 2
  # 自动释放超限的IP，默认不开启释放（开启 burstable ENI 的节点默认默认不会开启 IP 释放）
  release-excess-ips: true
  excess-ip-release-delay: 180

  cce-endpoint-gc-interval: 20s
  # cni 固定IP申请等待超时时间
  fixed-ip-allocate-timeout: 30s
  # 启用对远程固定IP的回收功能(开启后当系统发现远程记录有固定IP,但是k8s中没有与之对应的endpoint时,会删除远程固定IP)
  enable-remote-fixed-ip-gc: false
  # cni IP申请请求的超时时间
  ip-allocation-timeout: 30s
  # pod删除后固定ip保留时间
  fixed-ip-ttl-duration: 87600h

    # cni 接口限流
  # 扩展插件列表
  ext-cni-plugins:
  - "endpoint-probe"
  # - "cilium-cni"
  # - "portmap"