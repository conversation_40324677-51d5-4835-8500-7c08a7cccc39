{{- if or (eq .Values.DeploymentMod "standalone") (eq .Values.DeploymentMod "incluster") }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.2
  creationTimestamp: null
  name: netresourceconfigsets.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    categories:
    - cce
    kind: NetResourceConfigSet
    listKind: NetResourceConfigSetList
    plural: netresourceconfigsets
    shortNames:
    - nrcs
    singular: netresourceconfigset
  scope: Cluster
  versions:
  - name: v2alpha1
    schema:
      openAPIV3Schema:
        description: NetResourceConfigSet describes how to distribute network resources
          configuration to nodes.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              agent:
                description: Agent is the configuration of the agent.
                properties:
                  burstable-mehrfach-eni:
                    type: integer
                  enable-rdma:
                    type: boolean
                  eni-enterprise-security-group-id:
                    items:
                      type: string
                    type: array
                  eni-security-group-ids:
                    items:
                      type: string
                    type: array
                  eni-subnet-ids:
                    items:
                      type: string
                    type: array
                  eni-use-mode:
                    type: string
                  ext-cni-plugins:
                    items:
                      type: string
                    type: array
                  ippool-max-above-watermark:
                    type: integer
                  ippool-min-allocate:
                    type: integer
                  ippool-pre-allocate:
                    type: integer
                  ippool-pre-allocate-eni:
                    type: integer
                  mtu:
                    type: integer
                  release-excess-ips:
                    type: boolean
                  route-table-offset:
                    type: integer
                  use-eni-primary-address:
                    type: boolean
                type: object
              priority:
                description: Priority describes which object the target pod should
                  use when multiple objects affect a pod at the same time. The higher
                  the priority value, the earlier the object is configured. When multiple
                  objects have the same priority value, only the configuration of
                  the first object is taken.
                format: int32
                minimum: 0
                type: integer
              selector:
                description: Selector is a label query over node that should match
                  the node count.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
            required:
            - selector
            type: object
          status:
            properties:
              agentVersion:
                additionalProperties:
                  type: string
                description: AgentVersion is the config version of agent. key is name
                  of node value is resiversion of nrcs
                type: object
              nodeCount:
                description: NodeCount is the number of nodes was selected by this
                  NRCS.
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
{{- end }}