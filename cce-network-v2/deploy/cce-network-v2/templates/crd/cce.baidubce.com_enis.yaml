{{- if or (eq .Values.DeploymentMod "standalone") (eq .Values.DeploymentMod "incluster") }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.2
  creationTimestamp: null
  name: enis.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    categories:
    - cce
    kind: ENI
    listKind: ENIList
    plural: enis
    singular: eni
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: eni attached to node
      jsonPath: .spec.nodeName
      name: Node
      type: string
    - description: mode of eni
      jsonPath: .spec.useMode
      name: Mode
      type: string
    - description: status of eni
      jsonPath: .status.CCEStatus
      name: CStatus
      type: string
    - description: eni status of vpc
      jsonPath: .status.VPCStatus
      name: VStatus
      type: string
    - description: interface number on node
      jsonPath: .status.interfaceIndex
      name: INumber
      type: integer
    name: v2
    schema:
      openAPIV3Schema:
        description: This columns will be printed by exec `kubectl get enis`
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              borrowIPCount:
                description: BorrowIPCount
                type: integer
              description:
                description: description
                type: string
              enterpriseSecurityGroupIds:
                description: enterprise security group ids
                items:
                  type: string
                type: array
              id:
                description: ID unique identifier available for the resource
                type: string
              installSourceBasedRouting:
                description: InstallSourceBasedRouting install source based routing,
                  default false
                type: boolean
              instanceID:
                description: 'InstanceID insance ID of node resource, like ID of BCC.
                  This field represents the node name that ENI expects to attached.
                  Example: i-wWANcEYK'
                type: string
              ipv6PrivateIPSet:
                description: ipv6 private IP set
                items:
                  description: "PrivateIP VPC IP address \n swagger:model PrivateIP"
                  properties:
                    cidrAddress:
                      description: cidr address
                      type: string
                    gatewayIP:
                      description: gateway IP
                      type: string
                    primary:
                      description: primary
                      type: boolean
                    privateIPAddress:
                      description: private IP address
                      type: string
                    publicIPAddress:
                      description: public IP address
                      type: string
                    subnetID:
                      description: SubnetID subnet id of  private ip assigned with
                        this. When allocating IP across subnets, the subnet of private
                        IP may be different from ENI
                      type: string
                  type: object
                type: array
              macAddress:
                description: 'MacAddress mac address of eni instance After the ENI
                  is attached to the VM, the ENI device should be found in the VM
                  through this mac address Example: fa:26:00:0d:51:c7'
                type: string
              name:
                description: The name of eni was created by CCE and format as {clusterID}-{instance_name}-{randmon}
                type: string
              nodeName:
                description: ENI 要绑定的节点名
                type: string
              privateIPSet:
                description: private IP set
                items:
                  description: "PrivateIP VPC IP address \n swagger:model PrivateIP"
                  properties:
                    cidrAddress:
                      description: cidr address
                      type: string
                    gatewayIP:
                      description: gateway IP
                      type: string
                    primary:
                      description: primary
                      type: boolean
                    privateIPAddress:
                      description: private IP address
                      type: string
                    publicIPAddress:
                      description: public IP address
                      type: string
                    subnetID:
                      description: SubnetID subnet id of  private ip assigned with
                        this. When allocating IP across subnets, the subnet of private
                        IP may be different from ENI
                      type: string
                  type: object
                type: array
              routeTableOffset:
                default: 127
                description: RouteTableOffset route policy offset, default 127
                minimum: 0
                type: integer
              securityGroupIds:
                description: 'SecurityGroupIds list of security group IDs An ENI should
                  have at least one default security group Example: ["g-xpy9eitxhfib"]'
                items:
                  type: string
                type: array
              subnetID:
                description: 'SubnetID subnet id of eni instance In scenarios where
                  ENI is used across Subnet (such as PodSubnetTopologySpread), the
                  subnet ID of ENI may be different from secondry IP of the ENI. Example:
                  sbn-na1y2xryjyf3'
                type: string
              type:
                default: bcc
                description: Type eni type, default bcc
                type: string
              useMode:
                default: Secondary
                description: By default, the secondary IP mode is ENI, and pod IP
                  is the secondary IP of ENI The use scenario of this mode is to use
                  the virtual network device via the veth pair or IPVLAN
                type: string
              vpcID:
                description: 'VPCID vpc id of eni instance In scenarios where ENI
                  is used across VPCs (such as BCI), the VPC ID of ENI may be different
                  from that of the cluster Example: vpc-8nh1ks7a55a2'
                type: string
              vpcVersion:
                description: VPCVersion vpc version, default 0 data version of vpc,
                  used to determine whether the object needs to be updated
                format: int64
                type: integer
              zoneName:
                description: ZoneName zone name of eni instance
                type: string
            required:
            - nodeName
            - routeTableOffset
            - useMode
            type: object
          status:
            properties:
              CCEStatus:
                default: Pending
                type: string
              CCEStatusChangeLog:
                items:
                  description: ENIStatusChange history of ENIStatus. This is used
                    to track changes
                  properties:
                    CCEStatus:
                      description: state
                      type: string
                    VPCStatus:
                      type: string
                    code:
                      description: 'Code indicate type of status change Enum: [ok
                        failed]'
                      enum:
                      - ok
                      - failed
                      type: string
                    message:
                      description: Status message
                      type: string
                    time:
                      format: date-time
                      type: string
                  required:
                  - code
                  type: object
                maxItems: 20
                type: array
              VPCStatus:
                default: none
                type: string
              VPCStatusChangeLog:
                items:
                  description: ENIStatusChange history of ENIStatus. This is used
                    to track changes
                  properties:
                    CCEStatus:
                      description: state
                      type: string
                    VPCStatus:
                      type: string
                    code:
                      description: 'Code indicate type of status change Enum: [ok
                        failed]'
                      enum:
                      - ok
                      - failed
                      type: string
                    message:
                      description: Status message
                      type: string
                    time:
                      format: date-time
                      type: string
                  required:
                  - code
                  type: object
                maxItems: 20
                type: array
              endpointReference:
                description: Endpoint of eni associated In Primary use mode, Pod will
                  directly use the primary IP of ENI, so it needs to move the ENI
                  device directly to the network namespace of Pod. In order to avoid
                  possible failures during the creation of Pod, we need to record
                  the endpoint currently associated with ENI to further find ENI devices
                properties:
                  name:
                    type: string
                  namespace:
                    type: string
                  uid:
                    type: string
                type: object
              gatewayIPv4:
                description: GatewayIP is the interface's subnet's default route
                type: string
              gatewayIPv6:
                type: string
              index:
                description: Number is the ENI index, it is generated by the agent
                type: integer
              interfaceIndex:
                description: InterfaceIndex is the interface index, it used in combination
                  with FirstInterfaceIndex
                type: integer
              interfaceName:
                description: InterfaceName is the interface name, it is generated
                  by the agent
                type: string
              lendBorrowedIPCount:
                type: integer
              vpcVersion:
                description: VPCVersion vpc version, default 0 data version of vpc,
                  used to determine whether the object needs to be updated
                format: int64
                type: integer
            required:
            - CCEStatus
            - VPCStatus
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
{{- end }}