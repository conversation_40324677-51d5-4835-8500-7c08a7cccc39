# cce-network-v2

cce-network-v2 是基于百度云弹性网卡实现的 cni 插件。

## Introduction
------------

cce-network-v2 模式是百度云容器引擎CCE支持的扩展网络模式，基于百度云的弹性网卡产品，能够为集群内的 Pod 分配 VPC 内的 IP 地址。 由百度云 VPC 功能负责路由，打通容器网络的连通性，可实现 Pod 和 Node 的控制面和数据面完全在同一网络层面，该模式下的 Pod 能够复用百度云 VPC 所有产品特性。
；

## Prerequisites
-------------

- 百度云 1.18/1.20/1.22 Kubernetes集群
- 支持的 BCC 镜像包括 Centos7、Ubuntu16.04

## 支持的虚拟网卡类型
-------------

| 虚拟网卡类型 | 系统要求                                                                  | 说明                                                                          |
|--------------|---------------------------------------------------------------------------|-------------------------------------------------------------------------------|
| veth         | 适用于所有镜像                                                            | 该类型采用 veth pair 构建容器网络                                             |


## 节点最大 Pod 数

由于每个非 HostNetwork Pod 占用一个弹性网卡辅助 IP，因此每个节点上非 HostNetwork Pod 数量上限由节点内存和 CPU 核数共同决定，具体规则如下：

云主机可挂载弹性网卡数量=min（主机核数，8）

云主机绑定的网卡上可配置IP数量与内存相关：

|**内存** | **可分配 IP 数量** | 
|---|---|
| 1G | 1 | 
| （1-8]G  | 7 | 
| (8-32]G | 15 | 
| (32-64]G | 29 |
| 大于64G  | 39  |

例如 4C8G 的节点上最多可以挂载 4 张弹性网卡，每个弹性网卡的最大可分配 IP 数为 7，因此该节点上非 HostNetwork Pod 最大数量为 28。

**当前弹性网卡默认的配额是单个 VPC 内最多可以有 10 个弹性网卡，请提交工单联系 CCE 同学申请调大配额**

## ChangeLog
-------------

- 2.7.7 版本，支持在IPv6双栈容器网络