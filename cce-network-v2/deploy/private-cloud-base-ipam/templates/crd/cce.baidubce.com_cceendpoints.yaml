
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.2
  creationTimestamp: null
  name: cceendpoints.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    categories:
    - cce
    kind: CCEEndpoint
    listKind: CCEEndpointList
    plural: cceendpoints
    shortNames:
    - cep
    - ccep
    singular: cceendpoint
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: ip type
      jsonPath: .spec.network.ipAllocation.type
      name: Type
      type: string
    - description: ip release type
      jsonPath: .spec.network.ipAllocation.releaseStrategy
      name: Release Strategy
      type: string
    - description: Endpoint current state
      jsonPath: .status.state
      name: Endpoint State
      type: string
    - description: Endpoint IPv4 address
      jsonPath: .status.networking.addressing[0].ipv4
      name: IPv4
      type: string
    - description: Endpoint IPv6 address
      jsonPath: .status.networking.addressing[0].ipv6
      name: IPv6
      type: string
    name: v2
    schema:
      openAPIV3Schema:
        description: CCEEndpoint is the status of pod
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              external-identifiers:
                description: ExternalIdentifiers is a set of identifiers to identify
                  the endpoint apart from the pod name. This includes container runtime
                  IDs.
                properties:
                  container-id:
                    description: ID assigned by container runtime
                    type: string
                  container-name:
                    description: Name assigned to container
                    type: string
                  docker-endpoint-id:
                    description: Docker endpoint ID
                    type: string
                  docker-network-id:
                    description: Docker network ID
                    type: string
                  k8s-namespace:
                    description: K8s namespace for this endpoint
                    type: string
                  k8s-object-id:
                    type: string
                  k8s-pod-name:
                    description: K8s pod name for this endpoint
                    type: string
                  pod-name:
                    description: K8s pod for this endpoint(Deprecated, use K8sPodName
                      and K8sNamespace instead)
                    type: string
                type: object
              network:
                description: EndpointNetworkSpec Network config for CCE Endpoint
                properties:
                  ipAllocation:
                    properties:
                      node:
                        description: NodeIP is the IP of the node the endpoint is
                          running on. The IP must be reachable between nodes.
                        type: string
                      releaseStrategy:
                        default: TTL
                        description: 'IP address recycling policy TTL: represents
                          the default dynamic IP address recycling policy,default.
                          Never: this policy can only be used in fixed IP scenarios'
                        enum:
                        - TTL
                        - Never
                        type: string
                      ttlSecondsAfterDeleted:
                        description: TTLSecondsAfterFinished is the TTL duration after
                          this pod has been deleted when using fixed IP mode. This
                          field is only valid in the Fixed mode and the ReleaseStrategy
                          is TTL default is 7d
                        format: int64
                        type: integer
                      type:
                        default: Elastic
                        description: IPAllocType is the type for ip alloc strategy
                        enum:
                        - Elastic
                        - Fixed
                        - Manual
                        type: string
                      useIPV4:
                        type: boolean
                      useIPV6:
                        type: boolean
                    type: object
                type: object
            type: object
          status:
            description: EndpointStatus is the status of a CCE endpoint.
            properties:
              controllers:
                description: Controllers is the list of failing controllers for this
                  endpoint.
                items:
                  description: ControllerStatus is the status of a failing controller.
                  properties:
                    configuration:
                      description: Configuration is the controller configuration
                      properties:
                        error-retry:
                          description: Retry on error
                          type: boolean
                        error-retry-base:
                          description: 'Base error retry back-off time Format: duration'
                          format: int64
                          type: integer
                        interval:
                          description: 'Regular synchronization interval Format: duration'
                          format: int64
                          type: integer
                      type: object
                    name:
                      description: Name is the name of the controller
                      type: string
                    status:
                      description: Status is the status of the controller
                      properties:
                        consecutive-failure-count:
                          format: int64
                          type: integer
                        failure-count:
                          format: int64
                          type: integer
                        last-failure-msg:
                          type: string
                        last-failure-timestamp:
                          type: string
                        last-success-timestamp:
                          type: string
                        success-count:
                          format: int64
                          type: integer
                      type: object
                    uuid:
                      description: UUID is the UUID of the controller
                      type: string
                  type: object
                type: array
              external-identifiers:
                description: ExternalIdentifiers is a set of identifiers to identify
                  the endpoint apart from the pod name. This includes container runtime
                  IDs.
                properties:
                  container-id:
                    description: ID assigned by container runtime
                    type: string
                  container-name:
                    description: Name assigned to container
                    type: string
                  docker-endpoint-id:
                    description: Docker endpoint ID
                    type: string
                  docker-network-id:
                    description: Docker network ID
                    type: string
                  k8s-namespace:
                    description: K8s namespace for this endpoint
                    type: string
                  k8s-object-id:
                    type: string
                  k8s-pod-name:
                    description: K8s pod name for this endpoint
                    type: string
                  pod-name:
                    description: K8s pod for this endpoint(Deprecated, use K8sPodName
                      and K8sNamespace instead)
                    type: string
                type: object
              log:
                description: Log is the list of the last few warning and error log
                  entries
                items:
                  description: "EndpointStatusChange Indication of a change of status
                    \n swagger:model EndpointStatusChange"
                  properties:
                    code:
                      description: 'Code indicate type of status change Enum: [ok
                        failed]'
                      type: string
                    message:
                      description: Status message
                      type: string
                    state:
                      description: state
                      type: string
                    timestamp:
                      description: Timestamp when status change occurred
                      type: string
                  type: object
                type: array
              matchExpressions:
                description: A list of node selector requirements by node's labels.
                items:
                  description: A node selector requirement is a selector that contains
                    values, a key, and an operator that relates the key and values.
                  properties:
                    key:
                      description: The label key that the selector applies to.
                      type: string
                    operator:
                      description: Represents a key's relationship to a set of values.
                        Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and
                        Lt.
                      type: string
                    values:
                      description: An array of string values. If the operator is In
                        or NotIn, the values array must be non-empty. If the operator
                        is Exists or DoesNotExist, the values array must be empty.
                        If the operator is Gt or Lt, the values array must have a
                        single element, which will be interpreted as an integer. This
                        array is replaced during a strategic merge patch.
                      items:
                        type: string
                      type: array
                  required:
                  - key
                  - operator
                  type: object
                type: array
              networking:
                description: Networking is the networking properties of the endpoint.
                properties:
                  addressing:
                    description: IP4/6 addresses assigned to this Endpoint
                    items:
                      description: AddressPair is is a par of IPv4 and/or IPv6 address.
                      properties:
                        cidrs4:
                          description: CIDRs is a list of all CIDRs to which the IP
                            has direct access to. This is primarily useful if the
                            IP has been allocated out of a VPC subnet range and the
                            VPC provides routing to a set of CIDRs in which the IP
                            is routable.
                          items:
                            type: string
                          type: array
                        cidrs6:
                          items:
                            type: string
                          type: array
                        gatewayV4:
                          type: string
                        gatewayV6:
                          type: string
                        interface:
                          type: string
                        ipv4:
                          type: string
                        ipv6:
                          type: string
                        subnet:
                          type: string
                      type: object
                    type: array
                  node:
                    description: NodeIP is the IP of the node the endpoint is running
                      on. The IP must be reachable between nodes.
                    type: string
                required:
                - addressing
                type: object
              state:
                description: State is the state of the endpoint.
                type: string
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
