apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-cni-v2
  namespace: kube-system
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cce-cni-v2
rules:
  - apiGroups: [""]
    resources:
      ["pods", "nodes", "namespaces", "configmaps", "serviceaccounts", "events", "secrets", "services", "configmaps", "endpoints",
       "pods/status", "nodes/status", "namespaces"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["apps"]
    resources:
      ["statefulsets", "deployments", "replicasets"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "watch", "list"]
  - apiGroups: [ "coordination.k8s.io" ]
    resources:
      [ "leases" ]
    verbs: [ "get", "watch", "list", "update", "create", "patch" ]
  - apiGroups: ["cce.baidubce.com"]
    resources:
      - cceendpoints
      - netresourcesets
      - netresourcesets/status
    verbs: ["*"]
  - apiGroups: ["admissionregistration.k8s.io"]
    resources:
      - validatingwebhookconfigurations
      - mutatingwebhookconfigurations
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-cni-v2
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-cni-v2
subjects:
  - kind: ServiceAccount
    name: cce-cni-v2
    namespace: kube-system
